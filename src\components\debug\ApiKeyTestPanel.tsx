import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { apiKeyTester, TestSuite, TestResult } from '@/utils/apiKeyTester';

interface ApiKeyTestPanelProps {
  onClose?: () => void;
}

const ApiKeyTestPanel: React.FC<ApiKeyTestPanelProps> = ({ onClose }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [hasRun, setHasRun] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setHasRun(false);
    
    try {
      console.log('🧪 Starting API Key Tests...');
      const results = await apiKeyTester.runAllTests();
      setTestResults(results);
      setHasRun(true);
    } catch (error) {
      console.error('Test execution failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'PASS':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'FAIL':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'WARN':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'PASS':
        return 'text-green-600 dark:text-green-400';
      case 'FAIL':
        return 'text-red-600 dark:text-red-400';
      case 'WARN':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const totalStats = testResults.reduce(
    (acc, suite) => ({
      passed: acc.passed + suite.passed,
      failed: acc.failed + suite.failed,
      warnings: acc.warnings + suite.warnings,
    }),
    { passed: 0, failed: 0, warnings: 0 }
  );

  const totalTests = totalStats.passed + totalStats.failed + totalStats.warnings;
  const passRate = totalTests > 0 ? ((totalStats.passed / totalTests) * 100).toFixed(1) : 0;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Play className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Gemini API Key Test Suite
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Comprehensive testing for API key configuration and connectivity
              </p>
            </div>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <XCircle className="w-5 h-5 text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Test Controls */}
          <div className="mb-6">
            <button
              onClick={runTests}
              disabled={isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors"
            >
              {isRunning ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{isRunning ? 'Running Tests...' : 'Run API Key Tests'}</span>
            </button>
          </div>

          {/* Overall Summary */}
          {hasRun && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                Test Summary
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {totalStats.passed}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {totalStats.failed}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                    {totalStats.warnings}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Warnings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {passRate}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Pass Rate</div>
                </div>
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResults.map((suite, suiteIndex) => (
            <div key={suiteIndex} className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {suite.name}
                </h3>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-green-600 dark:text-green-400">
                    ✓ {suite.passed}
                  </span>
                  <span className="text-red-600 dark:text-red-400">
                    ✗ {suite.failed}
                  </span>
                  <span className="text-yellow-600 dark:text-yellow-400">
                    ⚠ {suite.warnings}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                {suite.tests.map((test, testIndex) => (
                  <div
                    key={testIndex}
                    className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        {getStatusIcon(test.status)}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900 dark:text-white">
                              {test.name}
                            </span>
                            <span className={`text-sm ${getStatusColor(test.status)}`}>
                              {test.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {test.message}
                          </p>
                          {test.details && (
                            <details className="mt-2">
                              <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                                Show Details
                              </summary>
                              <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-x-auto">
                                {JSON.stringify(test.details, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* Recommendations */}
          {hasRun && totalStats.failed > 0 && (
            <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                💡 Recommendations
              </h3>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• Set GEMINI_API_KEY environment variable</li>
                <li>• Example: export GEMINI_API_KEY=your_api_key_here</li>
                <li>• Restart development server after setting environment variables</li>
                <li>• Check browser console for detailed error messages</li>
                <li>• Verify API key is valid in Google AI Studio</li>
              </ul>
            </div>
          )}

          {/* Instructions */}
          {!hasRun && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
                🧪 Test Instructions
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                This test suite will verify your Gemini API key configuration and connectivity.
                Click "Run API Key Tests" to start the comprehensive testing process.
              </p>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Environment variable detection</li>
                <li>• API key format validation</li>
                <li>• SDK availability checks</li>
                <li>• Basic connectivity testing</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiKeyTestPanel;
