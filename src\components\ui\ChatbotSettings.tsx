import React, { useState } from 'react';
import { Settings, Download, Upload, Trash2, BarChart3, X } from 'lucide-react';
import { conversationManager } from '@/services/conversationManager';
import ConversationPersistenceService from '@/services/conversationPersistence';

interface ChatbotSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatbotSettings: React.FC<ChatbotSettingsProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'general' | 'data' | 'stats'>('general');
  const [importData, setImportData] = useState('');
  const [showImportDialog, setShowImportDialog] = useState(false);

  if (!isOpen) return null;

  const handleExportConversation = () => {
    const data = conversationManager.exportConversation();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nexus-conversation-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportConversation = () => {
    if (importData.trim()) {
      const success = conversationManager.importConversation(importData);
      if (success) {
        setImportData('');
        setShowImportDialog(false);
        alert('Conversation imported successfully!');
      } else {
        alert('Failed to import conversation. Please check the format.');
      }
    }
  };

  const handleClearAllData = () => {
    if (confirm('Are you sure you want to clear all stored conversation data? This cannot be undone.')) {
      ConversationPersistenceService.clearStoredConversations();
      alert('All conversation data cleared.');
    }
  };

  const storageInfo = ConversationPersistenceService.getStorageInfo();
  const conversationStats = conversationManager.getConversationStats();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-md max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            <span className="font-semibold">Chatbot Settings</span>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors p-1 rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'general', label: 'General', icon: <Settings className="w-4 h-4" /> },
            { id: 'data', label: 'Data', icon: <Download className="w-4 h-4" /> },
            { id: 'stats', label: 'Stats', icon: <BarChart3 className="w-4 h-4" /> }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center gap-2 p-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-96">
          {activeTab === 'general' && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  About Nexus
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nexus is an AI-powered portfolio assistant that helps visitors learn about Vansh Oberoi's skills, projects, and experience. It uses advanced AI models with intelligent fallback mechanisms.
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Features
                </h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• AI-powered responses with context awareness</li>
                  <li>• Smart suggestions based on conversation flow</li>
                  <li>• Automatic conversation persistence</li>
                  <li>• Portfolio-specific intelligence</li>
                  <li>• Fallback mode for reliability</li>
                </ul>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Storage
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {storageInfo.isAvailable 
                    ? `Conversations are automatically saved locally. Using ${Math.round(storageInfo.conversationData / 1024)}KB for chat data.`
                    : 'Local storage is not available. Conversations will not be saved.'
                  }
                </p>
              </div>
            </div>
          )}

          {activeTab === 'data' && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Export Conversation
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Download your current conversation as a JSON file.
                </p>
                <button
                  onClick={handleExportConversation}
                  className="flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm"
                >
                  <Download className="w-4 h-4" />
                  Export Current Conversation
                </button>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Import Conversation
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Load a previously exported conversation.
                </p>
                {!showImportDialog ? (
                  <button
                    onClick={() => setShowImportDialog(true)}
                    className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                  >
                    <Upload className="w-4 h-4" />
                    Import Conversation
                  </button>
                ) : (
                  <div className="space-y-2">
                    <textarea
                      value={importData}
                      onChange={(e) => setImportData(e.target.value)}
                      placeholder="Paste conversation JSON data here..."
                      className="w-full h-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-800 dark:text-white resize-none"
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={handleImportConversation}
                        className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                      >
                        Import
                      </button>
                      <button
                        onClick={() => {
                          setShowImportDialog(false);
                          setImportData('');
                        }}
                        className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Clear Data
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Remove all stored conversation data from your browser.
                </p>
                <button
                  onClick={handleClearAllData}
                  className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear All Data
                </button>
              </div>
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Conversation Statistics
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {conversationStats.totalConversations}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Total Conversations
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {conversationStats.totalMessages}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Total Messages
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {conversationStats.averageMessagesPerConversation}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Avg Messages/Chat
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {Math.round(storageInfo.conversationData / 1024)}KB
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Storage Used
                    </div>
                  </div>
                </div>
              </div>

              {conversationStats.newestConversation && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Recent Activity
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Last conversation: {new Date(conversationStats.newestConversation).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatbotSettings;
