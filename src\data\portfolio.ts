import { PortfolioData } from '@/types';

const portfolioData: PortfolioData = {
  personal: {
    name: '<PERSON><PERSON>',
    title: 'AIML Development Engineer',
    summary: 'Passionate AI Developer and Software Engineer with expertise in Python, Machine Learning, Django REST APIs, and Cloud Technologies. Currently working as an AI Developer & Software Developer Intern at EaseMyMed, developing cutting-edge AI solutions, RESTful APIs, implementing RAG for contextual AI interactions, and integrating advanced AI technologies including OpenAI and Gemini models.',
    image: '/profile.webp',
    contact: {
      address: '462 Model Town, Kapurthala, Punjab 144601',
      phone: '+91 9646570760',
      email: '<EMAIL>',
      socials: [
        {
          platform: 'LinkedIn',
          url: 'https://linkedin.com/in/Vansh462',
          icon: 'linkedin',
        },
        {
          platform: 'GitHub',
          url: 'https://github.com/Vansh462',
          icon: 'github',
        },
        {
          platform: 'Facebook',
          url: 'https://www.facebook.com/solo.learn.33',
          icon: 'facebook',
        },
        {
          platform: 'Twitter',
          url: 'https://twitter.com/vansh462?t=WqYNN77kovYrTQsPidKDyw&s=09',
          icon: 'twitter',
        },
        {
          platform: 'Instagram',
          url: 'https://www.instagram.com/vanshoberoi3103?igsh=YWxhbW9zMWJqeXBy',
          icon: 'instagram',
        },
      ],
    },
  },
  skills: [
    {
      name: 'Python',
      level: 95,
      icon: 'python',
      category: 'Programming',
    },
    {
      name: 'C++',
      level: 85,
      icon: 'code',
      category: 'Programming',
    },
    {
      name: 'C',
      level: 80,
      icon: 'code',
      category: 'Programming',
    },
    {
      name: 'HTML/CSS',
      level: 80,
      icon: 'html5',
      category: 'Web',
    },
    {
      name: 'SQL',
      level: 85,
      icon: 'database',
      category: 'Data',
    },
    {
      name: 'Cypher',
      level: 75,
      icon: 'database',
      category: 'Data',
    },
    {
      name: 'Machine Learning',
      level: 90,
      icon: 'brain',
      category: 'AI-ML',
    },
    {
      name: 'Large Language Models',
      level: 50,
      icon: 'brain',
      category: 'AI-ML',
    },
    {
      name: 'TensorFlow',
      level: 40,
      icon: 'brain',
      category: 'AI-ML',
    },
    {
      name: 'OpenCV',
      level: 80,
      icon: 'camera',
      category: 'AI-ML',
    },
    {
      name: 'Django',
      level: 88,
      icon: 'code',
      category: 'Frameworks',
    },
    {
      name: 'Scikit-learn',
      level: 90,
      icon: 'brain',
      category: 'AI-ML',
    },
    {
      name: 'AWS SageMaker',
      level: 60,
      icon: 'cloud',
      category: 'Cloud',
    },
    {
      name: 'Google Cloud Platform',
      level: 80,
      icon: 'cloud',
      category: 'Cloud',
    },
    {
      name: 'Docker',
      level: 80,
      icon: 'container',
      category: 'DevOps',
    },
    {
      name: 'Git',
      level: 35,
      icon: 'git',
      category: 'Tools',
    },
    {
      name: 'CI/CD',
      level: 50,
      icon: 'code',
      category: 'DevOps',
    },
    {
      name: 'GitHub',
      level: 70,
      icon: 'git',
      category: 'Tools',
    },
    {
      name: 'Documentation',
      level: 75,
      icon: 'code',
      category: 'Tools',
    },
    {
      name: 'Research',
      level: 70,
      icon: 'brain',
      category: 'AI-ML',
    },
  ],
  technologies: [
    { name: 'VS Code' },
    { name: 'Vim' },
    { name: 'Git' },
    { name: 'Android Studio' },
    { name: 'AWS SageMaker' },
    { name: 'AWS EC2' },
    { name: 'S3 buckets' },
    { name: 'GCP' },
    { name: 'Docker' },
    { name: 'Scikit-learn' },
    { name: 'Beautiful-Soup' },
    { name: 'TensorFlow' },
    { name: 'OpenCV' },
    { name: 'Computer Vision cv2' },
    { name: 'Linux (Kali/Arch/Mint)' },
    { name: 'Google-Colab' },
    { name: 'Django' },
    { name: 'GraphDB' },
    { name: 'GraphRAG' },
    { name: 'DataStax' },
    { name: 'Make.com' },
    { name: 'LangGraph' },
    { name: 'Ollama' },
    { name: 'OpenAI' },
    { name: 'Gemini' },
    { name: 'DeepSeek' },
    { name: 'GitHub Copilot' },
    { name: 'RooCode' },
    { name: 'Amazon Q Dev' },
    { name: 'cursorAI' },
    { name: 'Napkin.ai' },
    { name: 'draw.io' },
    { name: 'Notion' },
    { name: 'Multiprocessing' },
    { name: 'Threads' },
    { name: 'Streamlit' },
    { name: 'Pygame' },
    { name: 'NLTK' },
    { name: 'Pandas' },
    { name: 'NumPy' },
    { name: 'Matplotlib' },
    { name: 'Seaborn' },
    { name: 'Neo4j' },
    { name: 'Cypher' },
    { name: 'Bhashini AI' },
    { name: 'Selenium' },
    { name: 'CI/CD' },
    { name: 'NotebookLLM' },
    { name: 'ChatGPT' },
    { name: 'Claude' },
  ],
  experience: [
    {
      title: 'AIML Development Engineer Intern',
      company: 'EaseMyMed',
      startDate: 'Dec 2024',
      endDate: 'June 2025',
      description: [
        'Developed and managed RESTful APIs using Django REST framework for healthcare applications',
        'Integrated AI technologies including OpenAI (GPT-4o-mini) and Gemini (gemini-2.0-flash) models',
        'Developed custom AI functions to deepen understanding of core principles, bypassing LangChain where applicable',
        'Implemented RAG to cross reference and give right context to AI in-between consecutive calls',
        'Integrated Bhashini AI voice models and implemented benchmarking and feedback mechanisms',
        'Developed solutions for processing and analyzing images, PDFs, ZIP folders using JSON data structures',
        'Utilized AWS SageMaker for development and Google Cloud Console for deployment with Docker and CloudBuild',
        'Created daily technical documentation on Notion and researched new feature prototypes',
        'Worked extensively with libraries: requests, json, base64, pyMUPDF, genai, PIL, and more',
      ],
      technologies: [
        { name: 'Python' },
        { name: 'Django REST Framework' },
        { name: 'RESTful APIs' },
        { name: 'RAG Pipeline' },
        { name: 'OpenAI API' },
        { name: 'Gemini API' },
        { name: 'AWS SageMaker' },
        { name: 'Google Cloud Platform' },
        { name: 'Docker' },
        { name: 'Bhashini AI' },
        { name: 'Notion' },
      ],
    },
  ],
  projects: [
    {
      title: 'PromptWizard',
      description: 'Designed and implemented a custom frontend UI from scratch for Microsoft\'s prompt optimizer, focusing on user flow and clarity. Integrated backend API with the new UI and made the whole app deployable and hostable via a single command (Vercel-first deployment) following mono repo structure. Designed the "Test Values" feature to auto-fill sample data, allowing instant output previews for demo and debugging. Added functionality for users to select multiple features and instantly receive optimized prompts tailored to their needs.',
      technologies: [
        { name: 'Microsoft Backend' },
        { name: 'VibeCoded Custom UI' },
        { name: 'Vercel' },
        { name: 'API Integration' },
        { name: 'Mono Repo' },
      ],
      link: 'https://prompt-wizard-three.vercel.app/',
      github: 'https://github.com/Vansh462/PromptWizard',
      featured: true,
    },
    {
      title: 'Dr\'s Medicine Prescription Prediction',
      description: 'Built a supervised machine learning pipeline to predict appropriate medical prescriptions based on patient symptoms and demographic data. Started with a dataset containing approximately 5,921 patient records; after data cleaning and preprocessing, retained around 5,900 usable samples for modeling. Performed extensive exploratory data analysis (EDA) and feature engineering to identify key patterns. Achieved a test set accuracy of over 99% with the Random Forest model.',
      technologies: [
        { name: 'Python' },
        { name: 'pandas' },
        { name: 'scikit-learn' },
        { name: 'seaborn' },
        { name: 'matplotlib' },
        { name: 'Jupyter Notebook' },
      ],
      kaggle: 'https://kaggle.com/code/vanshoberoi3103/dr-s-medicine-prescription-prediction-model-99',
      featured: true,
    },
    {
      title: 'Jute Pest Classification',
      description: 'Fine-tuned a deep learning model TensorFlow\'s ResNet101x1 to classify 13 jute pest types with 95% accuracy on a test set of 379 images (6,443 training, 443 validation). Implemented preprocessing techniques: cropping, resizing, and pixel normalization. Optimized training on an AWS m5.large instance, achieving ~10s/epoch through hyperparameter tuning (learning rate, epochs, dropout) and prefetch() for reduced data loading time.',
      technologies: [
        { name: 'TensorFlow' },
        { name: 'ResNet101x1' },
        { name: 'AWS(m5.large)' },
        { name: 'Computer Vision' },
        { name: 'Deep Learning' },
      ],
      github: 'https://github.com/Vansh462/LearningProjects/tree/main/Jute%20Pest',
      kaggle: 'https://www.kaggle.com/code/vanshoberoi3103/jute-pest-tf-restnet101x1-95-acc-on-1st-try',
      featured: true,
    },
    {
      title: 'Sports Person Classification',
      description: 'Engineered a face-based sports person classifier leveraging HaarCascades (OpenCV) and wavelet transforms for robust image preprocessing, including face validation, cropping, and feature extraction. Conducted systematic hyperparameter optimization with GridSearchCV across SVC, RandomForest, and Logistic Regression models, utilizing StratifiedShuffleSplit for balanced validation. Finalized Logistic Regression as the optimal classifier, achieving 84.31% test accuracy.',
      technologies: [
        { name: 'scikit-learn' },
        { name: 'OpenCV(cv2)' },
        { name: 'matplotlib' },
        { name: 'GridSearchCV' },
        { name: 'HaarCascades' },
        { name: 'Wavelet' },
      ],
      github: 'https://github.com/Vansh462/LearningProjects/tree/main/SportsPersonClassifier',
      featured: true,
    },
    {
      title: 'Bombay House Price Prediction Site & Model',
      description: 'Developed a house price prediction model using Python and a machine learning model (linear regression). Calculation of House Price based on #rooms, Town Name, Air Conditioning, Parking, and so on. Built a Streamlit based web application for user interaction and deployed it on AWS Cloud EC2 instance. Configured Nginx on cloud as a reverse proxy for efficient application serving and scalability.',
      technologies: [
        { name: 'Python' },
        { name: 'ML' },
        { name: 'Streamlit' },
        { name: 'AWS Cloud' },
        { name: 'NginX' },
      ],
      github: 'https://github.com/Vansh462/LearningProjects/tree/main/BHP',
      featured: true,
    },
    {
      title: 'Tips App',
      description: 'Built a web-based tip prediction app using Streamlit as the frontend framework and deployed a pre-trained scikit-learn regression model (serialized with joblib). Designed an interactive UI with Streamlit\'s sidebar slider for real-time bill input and instant tip prediction output. Integrated model inference by loading a pickled estimator (tips.pkl) and generating predictions based on user input.',
      technologies: [
        { name: 'Streamlit' },
        { name: 'scikit-learn' },
        { name: 'joblib' },
        { name: 'Python' },
      ],
      github: 'https://github.com/Vansh462/LearningProjects/tree/main/Tips%20App',
      featured: false,
    },
    {
      title: 'Official Site Link Scraping from Name',
      description: 'Led a backend project to scrape official bank links using only the bank\'s name. Implemented Google library for efficient scraping, chosen after researching APIs and Selenium. Utilized multiprocessing for performance optimization in web scraping tasks.',
      technologies: [
        { name: 'Python' },
        { name: 'Selenium' },
        { name: 'Google-library' },
        { name: 'Beautiful-Soup' },
        { name: 'Multiprocessing' },
        { name: 'Threads' },
      ],
      featured: false,
    },
    {
      title: 'Pong Game (Pygame)',
      description: 'Implemented classic Pong gameplay using Python and Pygame, with full OOP design: custom classes for Ball, Player, Opponent, and GameManager. Sprite-based architecture: Leveraged pygame.sprite.Sprite for modular, reusable paddle and ball components. Collision detection and response: Handled precise ball-paddle and ball-wall collisions, including edge-case corrections for realistic bounce physics.',
      technologies: [
        { name: 'Python' },
        { name: 'Pygame' },
        { name: 'OOP' },
        { name: 'Game Development' },
      ],
      featured: false,
    },
    {
      title: 'Scrap & Sentimental Analysis',
      description: 'Built a robust rule-based NLP/text analytics pipeline in Python and Jupyter Notebook for the BlackCoffer assignment. Automated web scraping, text extraction, cleaning, and tokenization from articles using pandas, requests, BeautifulSoup, and NLTK. Created and leveraged vocabularies from pre-defined positive and negative wordlists (dictionary files) for sentiment analysis—no machine learning or statistical models were used.',
      technologies: [
        { name: 'Python' },
        { name: 'pandas' },
        { name: 'requests' },
        { name: 'BeautifulSoup' },
        { name: 'NLTK' },
        { name: 'Jupyter Notebook' },
      ],
      github: 'https://github.com/Vansh462/BlackCoffer',
      featured: false,
    },
  ],
  education: [
    {
      degree: 'Bachelor of Technology in Computer Science & Engineering',
      institution: 'Guru Nanak Dev University - State Government University',
      location: 'Amritsar, Punjab',
      startDate: 'Aug. 2022',
      endDate: 'June 2026',
      gpa: '3.8 (8.0/10.0)',
      description: 'UGC approved program with strong foundation in Advanced C++ programming, OOPS concepts (JAVA), Python, Data structures and algorithms, Design and Analysis of Algorithms, Operating system principles, and Basic networking concepts.',
    },
    {
      degree: 'Class XII - CBSE Board (88.4%)',
      institution: 'Montgomery Guru Nanak Public School',
      location: 'Kapurthala, Punjab',
      startDate: 'April 2020',
      endDate: 'June 2021',
      description: '88.4% CBSE Board',
    },
    {
      degree: 'Class X - CBSE Board (87.4%)',
      institution: 'Little Angles CO-ED Public School',
      location: 'Kapurthala, Punjab',
      startDate: 'April 2018',
      endDate: 'March 2019',
      description: '87.4% CBSE Board',
    },
  ],
  leadership: [
    {
      title: 'Graph Database Research & Learning',
      organization: 'Neo4j Workshops',
      date: 'June 2025',
      description: 'Researched and learned GraphDB and GraphRAG concepts through Neo4j workshops. Gained proficiency in Cypher query language. Understood the advantages of GraphDB for accurate data retrieval.',
    },
    {
      title: 'Backend Team Trainee',
      organization: 'ARAMBH Startup',
      date: 'Aug. 2024 – Oct. 2024',
      description: 'Collaborated with ECE colleagues on backend projects using Python. Applied and enhanced Python skills through assigned tasks.',
    },
    {
      title: 'Marketing Intern',
      organization: 'Corizo Company',
      date: 'June 2023 - July 2023',
      description: 'Found and Joined various online communities to market the Corizo group. Noted that groups made for marketing often don\'t yield results due to constant spamming, but groups targeting different topics like Sports, fitness, Cooking Classes, Founders office, etc yield more responsive results for digital marketing.',
    },
    {
      title: 'Volunteered for 7 day charity drive',
      organization: 'Hamari Pahchan NGO',
      date: 'June 2023',
      description: 'Fund raising, raising awareness, digital promotions. NGO provides education and skill development to underprivileged and offer internships for students.',
    },
    {
      title: 'Design Team Head',
      organization: 'GNDU E-Cell',
      date: 'Spring 2022 – 2023',
      description: 'Led a team of 4 members in creating innovative designs for diverse projects. Managed the design team and collaborated with other teams.',
    },
  ],
  testimonials: [
    {
      id: 1,
      content: "Good Work on Jute Pest project.",
      author: "Nikhil",
      position: "Working Professional, LearnFlu",
      linkedIn: "https://www.linkedin.com/in/nikhil-maurya-588945170/?originalSubdomain=in"
    },
    {
      id: 2,
      content: "Demonstrates disciplined and time-managed work delivery with good technical skills and strong potential. Shows excellent problem-solving abilities and should continue developing a broader strategic perspective to see projects through to completion.",
      author: "Harshraj",
      position: "CEO, Embea"
    },
    {
      id: 3,
      content: "I appreciate your thinking approach and the mentality you bring to challenges. Your problem-solving mindset and technical perspective make you someone I would definitely like to collaborate with in future ventures.",
      author: "Tarun",
      position: "CEO, Tekno Solve",
      linkedIn: "https://www.linkedin.com/in/tarun-singh666/"
    }
  ]
};

export default portfolioData;
