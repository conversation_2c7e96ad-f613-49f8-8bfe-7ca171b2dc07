import { ChatMessage, AIProvider, PortfolioData } from '@/types';
import portfolioData from '@/data/portfolio';

// AI Service Configuration
const AI_CONFIG = {
  maxRetries: 3,
  timeout: 30000,
  fallbackEnabled: true,
  streamingEnabled: false, // Disable streaming for now due to complexity
  providers: [
    {
      name: 'gemini',
      model: 'gemini-2.0-flash',
      available: true,
      priority: 1
    }
  ] as AIProvider[]
};

// Rate limiting configuration
const RATE_LIMIT = {
  maxRequestsPerMinute: 10,
  requestTimestamps: [] as number[]
};

// Error types for better error handling
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

// Portfolio context builder
class PortfolioContextBuilder {
  static buildContext(portfolioData: PortfolioData, focusAreas?: string[]): string {
    const context = {
      personal: portfolioData.personal,
      skills: portfolioData.skills,
      experience: portfolioData.experience,
      projects: portfolioData.projects.filter(p => p.featured),
      education: portfolioData.education,
      leadership: portfolioData.leadership
    };

    // If focus areas are specified, prioritize relevant information
    if (focusAreas && focusAreas.length > 0) {
      const focusedContext = this.filterByFocus(context, focusAreas);
      return JSON.stringify(focusedContext, null, 2);
    }

    return JSON.stringify(context, null, 2);
  }

  private static filterByFocus(context: any, focusAreas: string[]): any {
    const filtered = { ...context };
    
    // Filter skills by focus areas
    if (focusAreas.includes('skills') || focusAreas.includes('technologies')) {
      filtered.skills = context.skills;
    }
    
    // Filter projects by focus areas
    if (focusAreas.includes('projects')) {
      filtered.projects = context.projects;
    }
    
    // Add more filtering logic as needed
    return filtered;
  }
}

// Main AI Service class
export class AIService {
  private apiKey: string | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔧 AIService: Initializing...');

    try {
      // Enhanced environment variable detection with better logging
      const envCheck = this.detectEnvironmentVariables();
      this.apiKey = envCheck.activeApiKey;

      // Log comprehensive environment information
      console.log('🔍 AIService: Environment Analysis:', {
        runtime: envCheck.runtime,
        hasProcessEnv: envCheck.hasProcessEnv,
        systemApiKey: envCheck.systemApiKey ? {
          found: true,
          length: envCheck.systemApiKey.length,
          preview: envCheck.systemApiKey.substring(0, 10) + '...',
          isValid: this.validateApiKeyFormat(envCheck.systemApiKey)
        } : { found: false },
        buildTimeApiKey: envCheck.buildTimeApiKey ? {
          found: true,
          length: envCheck.buildTimeApiKey.length,
          preview: envCheck.buildTimeApiKey.substring(0, 10) + '...',
          isValid: this.validateApiKeyFormat(envCheck.buildTimeApiKey)
        } : { found: false },
        activeSource: envCheck.activeSource,
        mode: import.meta.env.MODE,
        dev: import.meta.env.DEV
      });

      if (this.apiKey) {
        const isValidFormat = this.validateApiKeyFormat(this.apiKey);
        console.log(`✅ AIService: API key loaded from ${envCheck.activeSource}`);
        console.log('🔑 AIService: API key validation:', {
          length: this.apiKey.length,
          preview: this.apiKey.substring(0, 10) + '...',
          formatValid: isValidFormat,
          expectedLength: '≥30 characters',
          expectedPrefix: 'AIza* or AI*'
        });

        if (!isValidFormat) {
          console.warn('⚠️ AIService: API key format may be invalid');
        }
      } else {
        console.warn('⚠️ AIService: No API key found in any environment source');
        this.logApiKeySetupInstructions();
      }
    } catch (error) {
      console.error('❌ AIService: Failed to initialize API key:', error);
      this.apiKey = null;
    }

    this.isInitialized = true;
    console.log('✅ AIService: Initialization complete. AI Available:', this.isAvailable());
  }

  /**
   * Detect and analyze environment variables
   */
  private detectEnvironmentVariables() {
    const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
    const runtime = hasProcessEnv ? 'node' : 'browser';

    // Check system environment variables
    const systemApiKey = hasProcessEnv ? process.env.GEMINI_API_KEY : null;

    // Check build-time environment variables
    const buildTimeApiKey = import.meta.env.GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY;

    // Determine active API key and source
    const activeApiKey = systemApiKey || buildTimeApiKey || null;
    const activeSource = systemApiKey ? 'system environment' :
                        buildTimeApiKey ? 'build-time environment' :
                        'none';

    return {
      runtime,
      hasProcessEnv,
      systemApiKey,
      buildTimeApiKey,
      activeApiKey,
      activeSource
    };
  }

  /**
   * Validate API key format
   */
  private validateApiKeyFormat(apiKey: string): boolean {
    if (!apiKey) return false;

    // Basic validation checks
    const hasValidLength = apiKey.length >= 30;
    const hasValidChars = /^[A-Za-z0-9_-]+$/.test(apiKey);
    const hasValidPrefix = apiKey.startsWith('AIza') || apiKey.startsWith('AI');

    return hasValidLength && hasValidChars && hasValidPrefix;
  }

  /**
   * Log API key setup instructions
   */
  private logApiKeySetupInstructions(): void {
    console.log('💡 AIService: API Key Setup Instructions:');
    console.log('   1. Get your API key from: https://makersuite.google.com/app/apikey');
    console.log('   2. Set environment variable:');
    console.log('      • Windows: set GEMINI_API_KEY=your_api_key_here');
    console.log('      • macOS/Linux: export GEMINI_API_KEY=your_api_key_here');
    console.log('   3. Restart your development server');
    console.log('   4. Refresh the page to reload the chatbot');
  }

  // Rate limiting check
  private checkRateLimit(): boolean {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Remove old timestamps
    RATE_LIMIT.requestTimestamps = RATE_LIMIT.requestTimestamps.filter(
      timestamp => timestamp > oneMinuteAgo
    );

    // Check if we're under the limit
    if (RATE_LIMIT.requestTimestamps.length >= RATE_LIMIT.maxRequestsPerMinute) {
      return false;
    }

    // Add current timestamp
    RATE_LIMIT.requestTimestamps.push(now);
    return true;
  }

  async generateResponse(
    messages: ChatMessage[],
    portfolioFocus?: string[]
  ): Promise<{ content: string; metadata: any }> {
    await this.initialize();

    // Check rate limiting
    if (!this.checkRateLimit()) {
      throw new AIServiceError(
        'Rate limit exceeded. Please wait a moment before sending another message.',
        'RATE_LIMIT_EXCEEDED',
        undefined,
        true
      );
    }

    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.sender !== 'user') {
      throw new AIServiceError('Invalid message format', 'INVALID_INPUT');
    }

    // Try each provider in order of priority with retries
    for (const provider of AI_CONFIG.providers) {
      if (!provider.available) continue;

      let lastError: Error | null = null;

      for (let attempt = 1; attempt <= AI_CONFIG.maxRetries; attempt++) {
        try {
          return await this.tryProvider(provider, messages, portfolioFocus);
        } catch (error) {
          lastError = error as Error;
          console.warn(`Provider ${provider.name} attempt ${attempt} failed:`, error);

          // If it's not retryable or last attempt, break
          if (!(error instanceof AIServiceError) || !error.retryable || attempt === AI_CONFIG.maxRetries) {
            break;
          }

          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }

      // If this was the last provider, throw the last error
      if (provider === AI_CONFIG.providers[AI_CONFIG.providers.length - 1] && lastError) {
        throw lastError;
      }
    }

    throw new AIServiceError('All AI providers failed', 'ALL_PROVIDERS_FAILED');
  }

  private async tryProvider(
    provider: AIProvider,
    messages: ChatMessage[],
    portfolioFocus?: string[]
  ): Promise<{ content: string; metadata: any }> {
    const startTime = Date.now();

    switch (provider.name) {
      case 'gemini':
        return await this.callGemini(messages, portfolioFocus, startTime);
      default:
        throw new AIServiceError(`Unknown provider: ${provider.name}`, 'UNKNOWN_PROVIDER');
    }
  }

  private async callGemini(
    messages: ChatMessage[],
    portfolioFocus?: string[],
    startTime: number
  ): Promise<{ content: string; metadata: any }> {
    if (!this.apiKey) {
      throw new AIServiceError('API key not available', 'NO_API_KEY');
    }

    // Check if Google Generative AI is available
    if (typeof window === 'undefined' || !(window as any).generativeai) {
      throw new AIServiceError('Google Generative AI not loaded', 'SDK_NOT_LOADED');
    }

    try {
      const genAI = new (window as any).generativeai.Defaults({ apiKey: this.apiKey });
      const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });

      const portfolioContext = PortfolioContextBuilder.buildContext(portfolioData, portfolioFocus);
      const conversationHistory = this.buildConversationHistory(messages);
      
      const prompt = this.buildPrompt(
        messages[messages.length - 1].content,
        portfolioContext,
        conversationHistory
      );

      const response = await model.generateContent(prompt);
      const content = await response.response.text();
      
      const processingTime = Date.now() - startTime;

      return {
        content: content.trim(),
        metadata: {
          model: 'gemini-2.0-flash',
          processingTime,
          provider: 'gemini',
          tokens: content.length // Approximate token count
        }
      };
    } catch (error: any) {
      throw new AIServiceError(
        `Gemini API error: ${error.message}`,
        'GEMINI_API_ERROR',
        'gemini',
        true
      );
    }
  }

  private buildConversationHistory(messages: ChatMessage[]): string {
    // Get last 5 messages for context (excluding the current one)
    const recentMessages = messages.slice(-6, -1);
    
    if (recentMessages.length === 0) return '';

    return recentMessages
      .map(msg => `${msg.sender === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
      .join('\n');
  }

  private buildPrompt(query: string, portfolioContext: string, conversationHistory: string): string {
    return `You are Nexus, an intelligent portfolio assistant for Vansh Oberoi. You help visitors learn about his skills, projects, experience, and background.

PERSONALITY:
- Professional yet friendly and approachable
- Knowledgeable about AI/ML and software development
- Concise but informative responses
- Enthusiastic about technology and learning

GUIDELINES:
- Keep responses under 150 words unless specifically asked for details
- Use specific examples from the portfolio when relevant
- If asked about something not in the portfolio, politely redirect to available information
- Suggest related topics the user might find interesting
- Use a conversational tone while maintaining professionalism

${conversationHistory ? `CONVERSATION HISTORY:\n${conversationHistory}\n` : ''}

PORTFOLIO DATA:
${portfolioContext}

USER QUESTION: ${query}

Please provide a helpful, accurate response based on the portfolio information above.`;
  }

  // Check if AI service is available
  isAvailable(): boolean {
    // AI is available if we have an API key, otherwise fallback mode is always available
    return this.isInitialized && (this.apiKey !== null || true); // Always allow fallback
  }

  // Get available providers
  getAvailableProviders(): AIProvider[] {
    return AI_CONFIG.providers.filter(p => p.available);
  }
}

// Export singleton instance
export const aiService = new AIService();
