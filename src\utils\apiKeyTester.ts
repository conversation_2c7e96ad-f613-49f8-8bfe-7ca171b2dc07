/**
 * Browser-compatible API Key Tester
 * 
 * This utility provides comprehensive testing for Gemini API key
 * configuration and functionality in the browser environment.
 */

export interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  details?: any;
  timestamp: string;
}

export interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  warnings: number;
}

export class ApiKeyTester {
  private results: TestSuite[] = [];

  /**
   * Run all API key tests
   */
  async runAllTests(): Promise<TestSuite[]> {
    console.log('🤖 Starting Gemini API Key Test Suite...');
    
    this.results = [];
    
    // Run test suites
    await this.testEnvironmentVariables();
    await this.testApiKeyFormat();
    await this.testSdkAvailability();
    await this.testApiConnectivity();
    
    this.generateReport();
    return this.results;
  }

  /**
   * Test environment variable detection
   */
  private async testEnvironmentVariables(): Promise<void> {
    const suite: TestSuite = {
      name: 'Environment Variables',
      tests: [],
      passed: 0,
      failed: 0,
      warnings: 0
    };

    // Test 1: Check process.env availability
    const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
    this.addTest(suite, {
      name: 'Process Environment Available',
      status: hasProcessEnv ? 'PASS' : 'WARN',
      message: hasProcessEnv ? 'process.env is accessible' : 'process.env not available (browser environment)',
      details: { hasProcessEnv, environment: 'browser' }
    });

    // Test 2: Check system environment variable
    const systemApiKey = hasProcessEnv ? process.env.GEMINI_API_KEY : null;
    this.addTest(suite, {
      name: 'System GEMINI_API_KEY',
      status: systemApiKey ? 'PASS' : 'WARN',
      message: systemApiKey ? 'System GEMINI_API_KEY found' : 'System GEMINI_API_KEY not found',
      details: {
        hasKey: !!systemApiKey,
        keyLength: systemApiKey?.length || 0,
        keyPreview: systemApiKey ? `${systemApiKey.substring(0, 10)}...` : null,
        source: 'system'
      }
    });

    // Test 3: Check build-time environment variables
    const buildTimeApiKey = import.meta.env.GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY;
    this.addTest(suite, {
      name: 'Build-time GEMINI_API_KEY',
      status: buildTimeApiKey ? 'PASS' : 'WARN',
      message: buildTimeApiKey ? 'Build-time GEMINI_API_KEY found' : 'Build-time GEMINI_API_KEY not found',
      details: {
        hasKey: !!buildTimeApiKey,
        keyLength: buildTimeApiKey?.length || 0,
        keyPreview: buildTimeApiKey ? `${buildTimeApiKey.substring(0, 10)}...` : null,
        source: 'build-time',
        mode: import.meta.env.MODE,
        dev: import.meta.env.DEV
      }
    });

    // Test 4: Determine active API key
    const activeApiKey = systemApiKey || buildTimeApiKey;
    this.addTest(suite, {
      name: 'Active API Key Resolution',
      status: activeApiKey ? 'PASS' : 'FAIL',
      message: activeApiKey ? 'API key resolved successfully' : 'No API key found',
      details: {
        hasActiveKey: !!activeApiKey,
        source: systemApiKey ? 'system' : buildTimeApiKey ? 'build-time' : 'none',
        priority: 'system > build-time'
      }
    });

    this.results.push(suite);
  }

  /**
   * Test API key format validation
   */
  private async testApiKeyFormat(): Promise<void> {
    const suite: TestSuite = {
      name: 'API Key Format',
      tests: [],
      passed: 0,
      failed: 0,
      warnings: 0
    };

    const apiKey = this.getActiveApiKey();

    if (!apiKey) {
      this.addTest(suite, {
        name: 'API Key Format Validation',
        status: 'FAIL',
        message: 'No API key available for format validation',
        details: { reason: 'missing_key' }
      });
      this.results.push(suite);
      return;
    }

    // Test 1: Length validation
    const isValidLength = apiKey.length >= 30;
    this.addTest(suite, {
      name: 'API Key Length',
      status: isValidLength ? 'PASS' : 'WARN',
      message: `API key length: ${apiKey.length} characters`,
      details: {
        length: apiKey.length,
        minExpected: 30,
        isValid: isValidLength
      }
    });

    // Test 2: Character composition
    const hasValidChars = /^[A-Za-z0-9_-]+$/.test(apiKey);
    this.addTest(suite, {
      name: 'Character Validation',
      status: hasValidChars ? 'PASS' : 'WARN',
      message: hasValidChars ? 'Valid character composition' : 'Contains unexpected characters',
      details: {
        pattern: '^[A-Za-z0-9_-]+$',
        matches: hasValidChars
      }
    });

    // Test 3: Prefix validation
    const hasValidPrefix = apiKey.startsWith('AIza') || apiKey.startsWith('AI');
    this.addTest(suite, {
      name: 'Prefix Validation',
      status: hasValidPrefix ? 'PASS' : 'WARN',
      message: hasValidPrefix ? 'Expected prefix found' : 'Non-standard prefix',
      details: {
        prefix: apiKey.substring(0, 4),
        expectedPrefixes: ['AIza', 'AI'],
        hasValidPrefix
      }
    });

    this.results.push(suite);
  }

  /**
   * Test SDK availability
   */
  private async testSdkAvailability(): Promise<void> {
    const suite: TestSuite = {
      name: 'SDK Availability',
      tests: [],
      passed: 0,
      failed: 0,
      warnings: 0
    };

    // Test 1: Check if Google Generative AI is loaded
    const hasGenerativeAI = typeof window !== 'undefined' && !!(window as any).generativeai;
    this.addTest(suite, {
      name: 'Google Generative AI SDK',
      status: hasGenerativeAI ? 'PASS' : 'FAIL',
      message: hasGenerativeAI ? 'SDK loaded successfully' : 'SDK not loaded',
      details: {
        hasSDK: hasGenerativeAI,
        windowObject: typeof window !== 'undefined',
        sdkPath: 'window.generativeai'
      }
    });

    // Test 2: Check SDK version/methods
    if (hasGenerativeAI) {
      const genAI = (window as any).generativeai;
      const hasDefaults = typeof genAI.Defaults === 'function';
      this.addTest(suite, {
        name: 'SDK Methods Available',
        status: hasDefaults ? 'PASS' : 'WARN',
        message: hasDefaults ? 'Required SDK methods found' : 'Some SDK methods missing',
        details: {
          hasDefaults,
          availableMethods: Object.keys(genAI || {})
        }
      });
    }

    this.results.push(suite);
  }

  /**
   * Test API connectivity
   */
  private async testApiConnectivity(): Promise<void> {
    const suite: TestSuite = {
      name: 'API Connectivity',
      tests: [],
      passed: 0,
      failed: 0,
      warnings: 0
    };

    const apiKey = this.getActiveApiKey();
    const hasSDK = typeof window !== 'undefined' && !!(window as any).generativeai;

    if (!apiKey || !hasSDK) {
      this.addTest(suite, {
        name: 'API Connection Test',
        status: 'FAIL',
        message: 'Cannot test connectivity - missing API key or SDK',
        details: {
          hasApiKey: !!apiKey,
          hasSDK,
          reason: !apiKey ? 'missing_api_key' : 'missing_sdk'
        }
      });
      this.results.push(suite);
      return;
    }

    try {
      // Test 1: Initialize Gemini client
      const genAI = new (window as any).generativeai.Defaults({ apiKey });
      this.addTest(suite, {
        name: 'Client Initialization',
        status: 'PASS',
        message: 'Gemini client initialized successfully',
        details: { clientType: 'GoogleGenerativeAI' }
      });

      // Test 2: Get model instance
      const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
      this.addTest(suite, {
        name: 'Model Instance',
        status: model ? 'PASS' : 'FAIL',
        message: model ? 'Model instance created' : 'Failed to create model instance',
        details: {
          modelName: 'gemini-2.0-flash',
          hasModel: !!model
        }
      });

      // Test 3: Simple API call (optional - commented out to avoid quota usage)
      /*
      try {
        const result = await model.generateContent('Hello');
        const response = await result.response;
        const text = response.text();
        
        this.addTest(suite, {
          name: 'API Response Test',
          status: 'PASS',
          message: 'API responded successfully',
          details: {
            responseLength: text.length,
            hasResponse: !!text
          }
        });
      } catch (apiError: any) {
        this.addTest(suite, {
          name: 'API Response Test',
          status: 'WARN',
          message: `API call failed: ${apiError.message}`,
          details: {
            error: apiError.message,
            note: 'This might be due to quota limits or network issues'
          }
        });
      }
      */

    } catch (error: any) {
      this.addTest(suite, {
        name: 'API Connection Test',
        status: 'FAIL',
        message: `Connection failed: ${error.message}`,
        details: {
          error: error.message,
          stack: error.stack
        }
      });
    }

    this.results.push(suite);
  }

  /**
   * Get the active API key
   */
  private getActiveApiKey(): string | null {
    const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
    const systemApiKey = hasProcessEnv ? process.env.GEMINI_API_KEY : null;
    const buildTimeApiKey = import.meta.env.GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY;
    return systemApiKey || buildTimeApiKey || null;
  }

  /**
   * Add a test result to a suite
   */
  private addTest(suite: TestSuite, test: Omit<TestResult, 'timestamp'>): void {
    const fullTest: TestResult = {
      ...test,
      timestamp: new Date().toISOString()
    };

    suite.tests.push(fullTest);

    if (test.status === 'PASS') suite.passed++;
    else if (test.status === 'FAIL') suite.failed++;
    else suite.warnings++;
  }

  /**
   * Generate and log test report
   */
  private generateReport(): void {
    console.log('\n🔍 API Key Test Report');
    console.log('========================\n');

    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;

    this.results.forEach(suite => {
      console.log(`📋 ${suite.name}:`);
      console.log(`  ✅ Passed: ${suite.passed}`);
      console.log(`  ❌ Failed: ${suite.failed}`);
      console.log(`  ⚠️  Warnings: ${suite.warnings}`);
      console.log('');

      totalPassed += suite.passed;
      totalFailed += suite.failed;
      totalWarnings += suite.warnings;
    });

    const total = totalPassed + totalFailed + totalWarnings;
    const passRate = total > 0 ? ((totalPassed / total) * 100).toFixed(1) : 0;

    console.log('📊 Overall Summary:');
    console.log(`  ✅ Total Passed: ${totalPassed}`);
    console.log(`  ❌ Total Failed: ${totalFailed}`);
    console.log(`  ⚠️  Total Warnings: ${totalWarnings}`);
    console.log(`  📈 Pass Rate: ${passRate}%`);
    console.log('');

    // Recommendations
    if (totalFailed > 0) {
      console.log('💡 Recommendations:');
      if (!this.getActiveApiKey()) {
        console.log('  • Set GEMINI_API_KEY environment variable');
        console.log('  • Example: export GEMINI_API_KEY=your_api_key_here');
      }
      console.log('  • Check browser console for detailed error messages');
      console.log('  • Restart development server after setting environment variables');
    }
  }

  /**
   * Get test results
   */
  getResults(): TestSuite[] {
    return this.results;
  }
}

// Export singleton instance
export const apiKeyTester = new ApiKeyTester();
