import React, { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, RotateCcw, Minimize2, Maximize2, Bar<PERSON>hart3, Settings, TestTube } from 'lucide-react';
import { ChatMessage as ChatMessageType, MessageStatus, ConversationContext } from '@/types';
import { conversationManager } from '@/services/conversationManager';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import ApiKeyTestPanel from '../debug/ApiKeyTestPanel';
import portfolioData from '@/data/portfolio';
import websiteIndex from '@/data/websiteIndex';
// Temporarily disabled for debugging
// import ConversationStats from './ConversationStats';
// import ChatbotSettings from './ChatbotSettings';

const Chatbot: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [input, setInput] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showApiTest, setShowApiTest] = useState(false);
  const [typingStatus, setTypingStatus] = useState<MessageStatus | null>(null);
  const [isAIAvailable, setIsAIAvailable] = useState(true);
  const [conversationContext, setConversationContext] = useState<ConversationContext | null>(null);
  const chatEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize conversation manager and load messages
  useEffect(() => {
    const initializeConversation = async () => {
      console.log('🎯 Chatbot: Initializing conversation...');

      // Check for system environment variable
      const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
      const systemApiKey = hasProcessEnv ? process.env.GEMINI_API_KEY : null;
      const buildTimeApiKey = import.meta.env.GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY;
      const activeApiKey = systemApiKey || buildTimeApiKey;

      console.log('🔍 Chatbot: Environment check:', {
        hasProcessEnv,
        hasSystemApiKey: !!systemApiKey,
        hasBuildTimeApiKey: !!buildTimeApiKey,
        activeApiKeyLength: activeApiKey?.length || 0,
        apiKeySource: systemApiKey ? 'system' : buildTimeApiKey ? 'build-time' : 'none',
        mode: import.meta.env.MODE,
        dev: import.meta.env.DEV
      });

      try {
        // Note: ConversationManager doesn't have initialize method, it initializes in constructor
        const loadedMessages = conversationManager.getMessages();
        setMessages(loadedMessages);
        setConversationContext(conversationManager.getContext());
        setIsAIAvailable(conversationManager.isAIAvailable());

        console.log('✅ Chatbot: Conversation initialized successfully');
        console.log('📝 Chatbot: Loaded messages count:', loadedMessages.length);
        console.log('🤖 Chatbot: AI available:', conversationManager.isAIAvailable());

        // If no messages, the ConversationManager already creates a welcome message
      } catch (error) {
        console.error('❌ Chatbot: Failed to initialize conversation:', error);
        setIsAIAvailable(false);

        // Fallback welcome message
        const hasSystemApiKey = typeof process !== 'undefined' && process.env?.GOOGLE_API_KEY;
        const hasBuildTimeApiKey = import.meta.env.GOOGLE_API_KEY || import.meta.env.VITE_GOOGLE_API_KEY;
        const hasApiKey = hasSystemApiKey || hasBuildTimeApiKey;

        const welcomeMessage = hasApiKey
          ? "Hi! I'm Nexus, Vansh's portfolio assistant. I can help you learn about his skills, projects, experience, and more! ✨"
          : "Hi! I'm Nexus, Vansh's portfolio assistant. I'm running in fallback mode (set GEMINI_API_KEY system variable for AI features). I can still help you learn about Vansh's skills, projects, and experience! ✨";

        setMessages([{
          id: Date.now().toString(),
          content: welcomeMessage,
          sender: 'assistant',
          timestamp: new Date(),
          status: 'sent'
        }]);

        console.log('🔄 Chatbot: Using fallback welcome message');
      }
    };

    initializeConversation();
  }, []);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (isOpen && !isMinimized && chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isOpen, isMinimized, typingStatus]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  const handleSend = async () => {
    const query = input.trim();
    if (!query) return;

    setInput('');
    setTypingStatus('typing');

    try {
      await conversationManager.sendMessage(query);
      setMessages(conversationManager.getMessages());
      setConversationContext(conversationManager.getContext());
    } catch (error) {
      console.error('Failed to send message:', error);
      // Fallback to simple response
      const userMessage: ChatMessageType = {
        id: Date.now().toString(),
        content: query,
        sender: 'user',
        timestamp: new Date(),
        status: 'sent'
      };

      const response = generateSmartResponse(query);
      const assistantMessage: ChatMessageType = {
        id: (Date.now() + 1).toString(),
        content: response,
        sender: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      };

      setMessages(prev => [...prev, userMessage, assistantMessage]);
    } finally {
      setTypingStatus(null);
    }
  };

  // Enhanced response generator with navigation and formatting
  const generateSmartResponse = (query: string): string => {
    const lowerQuery = query.toLowerCase();

    // Handle scraping project queries
    if (lowerQuery.includes('scrap') || lowerQuery.includes('scraping')) {
      navigateToSection('#projects-section');
      setTimeout(() => {
        // Try to highlight specific project elements if they exist
        const scrapingProjects = document.querySelectorAll('[data-project*="scraping"], [data-project*="scrap"]');
        scrapingProjects.forEach(el => highlightElement(`#${el.id}`));
      }, 1000);

      return `🔍 **Yes! I have worked on several scraping projects:**

**🏦 Official Site Link Scraping from Name**
• Led a backend project to scrape official bank links using only the bank's name
• Implemented Google library for efficient scraping, chosen after researching APIs and Selenium
• Utilized multiprocessing for performance optimization in web scraping tasks
• **Technologies:** Python, Selenium, Google-library, Beautiful-Soup, Multiprocessing, Threads

**📊 Scrap & Sentimental Analysis (BlackCoffer Assignment)**
• Built a robust rule-based NLP/text analytics pipeline in Python and Jupyter Notebook
• Automated web scraping, text extraction, cleaning, and tokenization from articles
• Created and leveraged vocabularies from pre-defined positive and negative wordlists
• **Technologies:** Python, pandas, requests, BeautifulSoup, NLTK, Jupyter Notebook
• **GitHub:** https://github.com/Vansh462/BlackCoffer

*Navigated to projects section! Check out these scraping solutions above! 👆*`;
    }

    // Handle specific project queries
    if (lowerQuery.includes('promptwizard') || lowerQuery.includes('prompt wizard')) {
      navigateToSection('#project-promptwizard');
      return `🧙‍♂️ **PromptWizard - AI Prompt Optimizer**

• Custom frontend UI for Microsoft's prompt optimizer
• Vercel-first deployment with mono repo structure
• "Test Values" feature for instant output previews
• Multi-feature selection for optimized prompts

**Tech Stack:** Microsoft Backend, Custom UI, Vercel, API Integration
🔗 **Live Demo:** https://prompt-wizard-three.vercel.app/

*Navigated to the project section! 👆*`;
    }

    if (lowerQuery.includes('medical') || lowerQuery.includes('prescription') || lowerQuery.includes('doctor')) {
      navigateToSection('#project-prescription');
      return `🏥 **Dr's Medicine Prescription Prediction**

• Supervised ML pipeline for medical prescription prediction
• 99%+ accuracy with Random Forest model
• Processed 5,900+ patient records with extensive EDA
• Advanced feature engineering and data preprocessing

**Tech Stack:** Python, pandas, scikit-learn, seaborn, matplotlib
📊 **Kaggle:** Available with detailed analysis

*Check out the project details above! 👆*`;
    }

    // Handle skills queries
    if (lowerQuery.includes('skill') || lowerQuery.includes('technology')) {
      navigateToSection('#skills-section');

      if (lowerQuery.includes('python')) {
        highlightElement('#skill-python');
        return `🐍 **Python Expertise**

I'm highly proficient in Python with extensive experience in:
• **AI/ML:** TensorFlow, scikit-learn, pandas, NumPy
• **Web Scraping:** Selenium, Beautiful Soup, requests
• **Web Development:** Django, Django REST Framework
• **Data Analysis:** matplotlib, seaborn, Jupyter Notebook

*Highlighted Python in the skills section! 👆*`;
      }

      if (lowerQuery.includes('ai') || lowerQuery.includes('ml') || lowerQuery.includes('machine learning')) {
        highlightElement('#skills-ai-ml');
        return `🤖 **AI/ML Expertise**

• **Large Language Models:** OpenAI GPT-4o-mini, Gemini 2.0-flash
• **Machine Learning:** TensorFlow, scikit-learn, Computer Vision
• **Deep Learning:** ResNet101x1, CNN architectures
• **RAG Implementation:** Custom AI functions, context management
• **Model Deployment:** AWS SageMaker, Google Cloud Platform

*Navigated to AI/ML skills! 👆*`;
      }

      return `💻 **Technical Skills Overview**

**Programming:** Python, C++, C, HTML/CSS, SQL, Cypher
**AI/ML:** TensorFlow, OpenCV, scikit-learn, Large Language Models
**Cloud:** AWS SageMaker, Google Cloud Platform, Docker
**Tools:** Git, VS Code, Notion, Linux, Selenium

*Check out the complete skills section above! 👆*`;
    }

    // Handle project queries
    if (lowerQuery.includes('project')) {
      navigateToSection('#projects-section');
      return `🚀 **Featured Projects Portfolio**

**🧙‍♂️ PromptWizard** - AI prompt optimization tool
**🏥 Medical Prediction** - 99% accuracy ML model
**🌾 Jute Pest Classification** - 95% accuracy with ResNet101x1
**🏃‍♂️ Sports Person Classifier** - Face-based classification
**🏠 House Price Prediction** - Streamlit web app on AWS
**🔍 Web Scraping Projects** - Multiple scraping solutions

*Navigated to projects section! Which one interests you most?*`;
    }

    // Handle experience queries
    if (lowerQuery.includes('experience') || lowerQuery.includes('work') || lowerQuery.includes('job')) {
      navigateToSection('#experience-section');
      return `💼 **Professional Experience**

**🏥 AIML Development Engineer Intern @ EaseMyMed**
*Dec 2024 - June 2025*

• Developed RESTful APIs using Django REST framework
• Integrated OpenAI GPT-4o-mini and Gemini 2.0-flash models
• Implemented custom RAG pipeline for context management
• AWS SageMaker development & Google Cloud deployment
• Bhashini AI voice models integration

*Check out the detailed experience above! 👆*`;
    }

    // Handle education queries
    if (lowerQuery.includes('education') || lowerQuery.includes('study') || lowerQuery.includes('university')) {
      navigateToSection('#education-section');
      return `🎓 **Educational Background**

**B.Tech Computer Science & Engineering**
*Guru Nanak Dev University (2022-2026)*
• GPA: 3.8/4.0 (8.0/10.0)
• Advanced C++, OOPS (Java), Python, DSA
• Operating Systems, Networking, Algorithm Design

*Navigated to education section! 👆*`;
    }

    // Handle contact queries
    if (lowerQuery.includes('contact') || lowerQuery.includes('reach') || lowerQuery.includes('email') || lowerQuery.includes('hire')) {
      navigateToSection('#contact-section');
      return `📧 **Let's Connect!**

Ready to collaborate or discuss opportunities?

**📧 Email:** Available in contact section
**💼 LinkedIn:** Professional profile linked
**🐙 GitHub:** Check out my code repositories
**📱 Social Media:** Multiple platforms available

*Navigated to contact section! 👆*`;
    }

    // Handle greetings
    if (lowerQuery.includes('hello') || lowerQuery.includes('hi') || lowerQuery.includes('hey')) {
      return `👋 **Hello! Great to meet you!**

I'm Nexus, Vansh's AI portfolio assistant. I can help you explore:

🔍 **Scraping Projects** - Web scraping and data extraction
🤖 **AI/ML Projects** - Machine learning and deep learning
💻 **Technical Skills** - Programming and technologies
💼 **Work Experience** - Professional background
🎓 **Education** - Academic achievements

**What would you like to explore first?**`;
    }

    // Default response with suggestions
    return `🤔 **Interesting question!**

I can help you discover Vansh's expertise in:

• **🔍 Scraping Projects** - "Tell me about scraping projects"
• **🤖 AI/ML Work** - "What AI projects have you built?"
• **💻 Technical Skills** - "What technologies do you use?"
• **💼 Experience** - "Tell me about your work experience"
• **🎓 Education** - "What's your educational background?"

**Try asking about any of these topics!**`;
  };

  // Navigation helper function
  const navigateToSection = (target: string) => {
    setTimeout(() => {
      // Try multiple possible selectors
      const selectors = [
        target,
        target.replace('#', '[id="') + '"]',
        target.replace('#', '[data-section="') + '"]',
        target.replace('#', '.') // Try class selector
      ];

      let element = null;
      for (const selector of selectors) {
        element = document.querySelector(selector);
        if (element) break;
      }

      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        console.log(`Navigated to: ${target}`);
      } else {
        console.warn(`Could not find element: ${target}`);
        // Try to find any projects section
        const projectsSection = document.querySelector('[id*="project"], [class*="project"], section');
        if (projectsSection) {
          projectsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          console.log('Navigated to fallback projects section');
        }
      }
    }, 500);
  };

  // Highlight helper function
  const highlightElement = (target: string) => {
    setTimeout(() => {
      const element = document.querySelector(target);
      if (element) {
        const originalBg = (element as HTMLElement).style.backgroundColor;
        const originalTransition = (element as HTMLElement).style.transition;

        (element as HTMLElement).style.transition = 'background-color 0.3s ease';
        (element as HTMLElement).style.backgroundColor = 'rgba(59, 130, 246, 0.2)'; // Blue highlight

        setTimeout(() => {
          (element as HTMLElement).style.backgroundColor = originalBg;
          (element as HTMLElement).style.transition = originalTransition;
        }, 3000);

        console.log(`Highlighted element: ${target}`);
      } else {
        console.warn(`Could not highlight element: ${target}`);
      }
    }, 100);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleClearConversation = async () => {
    try {
      conversationManager.clearConversation();
      await conversationManager.sendMessage("", true); // Send empty message to trigger welcome
      setMessages(conversationManager.getMessages());
      setConversationContext(conversationManager.getContext());
    } catch (error) {
      console.error('Failed to clear conversation:', error);
      // Fallback
      setMessages([{
        id: Date.now().toString(),
        content: "Hi! I'm Nexus, Vansh's portfolio assistant. I can help you learn about his skills, projects, experience, and more! ✨",
        sender: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      }]);
    }
    setShowStats(false);
  };

  // Debug function to show API key status
  const handleDebugApiKey = () => {
    console.log('🔍 DEBUG: API Key Information');
    console.log('=================================');
    console.log('System Environment Variables:');

    // Check system environment
    const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
    const systemApiKey = hasProcessEnv ? process.env.GEMINI_API_KEY : null;
    const buildTimeApiKey = import.meta.env.GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY;

    console.log('- process.env available:', hasProcessEnv);
    console.log('- GEMINI_API_KEY (system):', systemApiKey ? '***PRESENT***' : 'NOT_FOUND');
    console.log('- GEMINI_API_KEY (build-time):', buildTimeApiKey ? '***PRESENT***' : 'NOT_FOUND');
    console.log('- MODE:', import.meta.env.MODE);
    console.log('- DEV:', import.meta.env.DEV);

    const activeApiKey = systemApiKey || buildTimeApiKey;
    if (activeApiKey) {
      console.log('- Active API Key Length:', activeApiKey.length);
      console.log('- Active API Key Preview:', activeApiKey.substring(0, 10) + '...');
      console.log('- Source:', systemApiKey ? 'System Environment' : 'Build-time Environment');
    }

    console.log('=================================');
    console.log('AI Service Status:', conversationManager.isAIAvailable());

    // Show alert to user
    const status = activeApiKey
      ? `✅ API Key Found (${systemApiKey ? 'System' : 'Build-time'})`
      : '❌ API Key Not Found';
    alert(`API Key Status:\n${status}\n\nTo set system variable:\nexport GEMINI_API_KEY=your_key\n\nCheck console for details`);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
    if (!isMinimized) {
      setShowStats(false);
    }
  };

  const toggleStats = () => {
    setShowStats(!showStats);
  };

  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setIsMinimized(false);
      setShowStats(false);
      setShowSettings(false);
    }
  };

  return (
    <>
      {/* Floating Chatbot Button */}
      <button
        className="fixed bottom-6 right-6 z-50 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl shadow-2xl p-4 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 hover:scale-105 hover:shadow-3xl group"
        onClick={toggleChat}
        aria-label={isOpen ? "Close chatbot" : "Open chatbot"}
      >
        <div className="relative">
          {isOpen ? (
            <svg className="w-6 h-6 transition-transform duration-200 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6 transition-transform duration-200 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          )}
          {!isOpen && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          )}
        </div>
      </button>

      {/* Chatbot Panel */}
      {isOpen && (
        <div className={`fixed bottom-20 right-6 z-50 w-[440px] max-w-[calc(100vw-3rem)] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300 ${
          isMinimized ? 'h-16' : 'h-[650px]'
        }`}>
          {/* Header */}
          <div className="flex items-center justify-between px-5 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-slate-800 to-slate-900 text-white rounded-t-2xl">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div>
                <span className="font-semibold text-sm">Nexus Assistant</span>
                <div className="flex items-center gap-1 text-xs text-gray-300">
                  <div className={`w-1.5 h-1.5 rounded-full ${isAIAvailable ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                  <span>{isAIAvailable ? 'AI Mode' : 'Fallback Mode'}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={handleDebugApiKey}
                className="p-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200"
                aria-label="Debug API Key"
                title="Debug API Key Status"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              <button
                onClick={() => setShowApiTest(true)}
                className="p-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200"
                aria-label="Test API Key"
                title="Run API Key Tests"
              >
                <TestTube className="w-4 h-4" />
              </button>
              <button
                onClick={toggleStats}
                className={`p-2 rounded-lg transition-all duration-200 ${showStats ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white hover:bg-white/10'}`}
                aria-label="Toggle conversation stats"
                title="Show conversation stats"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </button>
              <button
                onClick={handleClearConversation}
                className="p-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200"
                aria-label="Clear conversation"
                title="Clear conversation"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
              <button
                onClick={toggleMinimize}
                className="p-2 rounded-lg text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200"
                aria-label={isMinimized ? "Maximize chat" : "Minimize chat"}
              >
                {isMinimized ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12l-1.411-1.411L16 13.177V10.5a1.5 1.5 0 00-3 0v3.677l-2.589-2.588L9 12l5 5 5-5z" />
                  </svg>
                )}
              </button>
              <button
                onClick={toggleChat}
                className="p-2 rounded-lg text-white/70 hover:text-white hover:bg-red-500/20 transition-all duration-200"
                aria-label="Close chat"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Messages Area */}
          {!isMinimized && (
            <>
              <div className="flex-1 overflow-y-auto px-4 py-3 bg-gradient-to-b from-gray-50 to-white dark:from-gray-800/30 dark:to-gray-900/50 space-y-3 min-h-0">
                {messages.map((message, index) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isLatest={index === messages.length - 1}
                  />
                ))}
                {typingStatus === 'typing' && <TypingIndicator />}
                <div ref={chatEndRef} />
              </div>

              {/* Conversation Stats - Temporarily disabled */}
              {showStats && (
                <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Stats feature coming soon...
                  </div>
                </div>
              )}

              {/* Input Area - Streamlined */}
              <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                <div className="p-3">
                  <div className="flex gap-2 items-center">
                    <div className="flex-1">
                      <input
                        ref={inputRef}
                        type="text"
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 text-sm"
                        placeholder="Ask about skills, projects, experience..."
                        value={input}
                        onChange={e => setInput(e.target.value)}
                        onKeyPress={handleKeyPress}
                        disabled={typingStatus === 'typing'}
                      />
                    </div>
                    <button
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-2.5 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg disabled:shadow-none"
                      onClick={handleSend}
                      disabled={!input.trim() || typingStatus === 'typing'}
                    >
                      {typingStatus === 'typing' ? (
                        <RotateCcw className="w-4 h-4 animate-spin" />
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>

                {/* Quick Actions - Compact */}
                <div className="px-3 pb-2">
                  <div className="grid grid-cols-2 gap-1.5">
                    <button
                      onClick={() => handleSuggestionClick("Did you do any scraping projects?")}
                      className="flex items-center gap-2 p-2 text-left text-xs bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 rounded-lg transition-all duration-200 border border-blue-200/50 dark:border-blue-700/50 hover:border-blue-300 dark:hover:border-blue-600 group"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-md flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 flex-1 font-medium text-xs">
                        Scraping Projects
                      </span>
                    </button>
                    <button
                      onClick={() => handleSuggestionClick("What AI projects have you built?")}
                      className="flex items-center gap-2 p-2 text-left text-xs bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/30 dark:hover:to-pink-900/30 rounded-lg transition-all duration-200 border border-purple-200/50 dark:border-purple-700/50 hover:border-purple-300 dark:hover:border-purple-600 group"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-md flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 flex-1 font-medium text-xs">
                        AI/ML Projects
                      </span>
                    </button>
                    <button
                      onClick={() => handleSuggestionClick("What are your main skills?")}
                      className="flex items-center gap-2 p-2 text-left text-xs bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 hover:from-green-100 hover:to-emerald-100 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 rounded-lg transition-all duration-200 border border-green-200/50 dark:border-green-700/50 hover:border-green-300 dark:hover:border-green-600 group"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-md flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                        </svg>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 flex-1 font-medium text-xs">
                        Technical Skills
                      </span>
                    </button>
                    <button
                      onClick={() => handleSuggestionClick("Tell me about your work experience")}
                      className="flex items-center gap-2 p-2 text-left text-xs bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 hover:from-orange-100 hover:to-red-100 dark:hover:from-orange-900/30 dark:hover:to-red-900/30 rounded-lg transition-all duration-200 border border-orange-200/50 dark:border-orange-700/50 hover:border-orange-300 dark:hover:border-orange-600 group"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-orange-500 to-red-600 rounded-md flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                        </svg>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 flex-1 font-medium text-xs">
                        Work Experience
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Settings Panel - Temporarily disabled */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Settings</h3>
              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Settings panel coming soon...
            </p>
          </div>
        </div>
      )}

      {/* API Key Test Panel */}
      {showApiTest && (
        <ApiKeyTestPanel onClose={() => setShowApiTest(false)} />
      )}
    </>
  );
};

export default Chatbot;
