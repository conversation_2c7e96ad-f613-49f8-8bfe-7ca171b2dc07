import { Chat<PERSON>essage, ConversationContext, MessageSender, MessageStatus } from '@/types';
import { aiService, AIServiceError } from './aiService';
import { ChatbotError<PERSON><PERSON><PERSON>, EnhancedFallbackGenerator } from './errorHandler';
import PortfolioIntelligenceService from './portfolioIntelligence';
import ConversationPersistenceService from './conversationPersistence';
import websiteIndex from '@/data/websiteIndex';

// Generate unique IDs for messages and sessions
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Fallback response generator for when AI is unavailable
class FallbackResponseGenerator {
  static generateResponse(query: string): string {
    const lowerQuery = query.toLowerCase();
    
    // Check for greetings
    if (this.isGreeting(lowerQuery)) {
      return "Hi! I'm <PERSON><PERSON><PERSON>, <PERSON><PERSON>'s portfolio assistant. I can help you learn about his skills, projects, experience, and background. What would you like to know?";
    }

    // Check for specific portfolio sections
    for (const [keyword, config] of Object.entries(websiteIndex)) {
      if (lowerQuery.includes(keyword.toLowerCase())) {
        this.triggerUIAction(config);
        return this.getKeywordResponse(keyword);
      }
    }

    // General help response
    if (this.isHelpRequest(lowerQuery)) {
      return "I can help you with information about:\n• Skills and technologies\n• Projects and achievements\n• Work experience\n• Education and background\n• Contact information\n\nWhat would you like to explore?";
    }

    // Default response
    return "I'm here to help you learn about Vansh's portfolio! You can ask me about his skills, projects, experience, education, or any specific technologies you're interested in.";
  }

  private static isGreeting(query: string): boolean {
    const greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => query.includes(greeting));
  }

  private static isHelpRequest(query: string): boolean {
    const helpKeywords = ['help', 'what can you do', 'how can you help', 'what do you know'];
    return helpKeywords.some(keyword => query.includes(keyword));
  }

  private static triggerUIAction(config: any): void {
    if (config.action === 'navigate' || config.action === 'highlight') {
      const element = document.querySelector(config.target);
      if (element) {
        if (config.action === 'navigate') {
          element.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Add highlight effect
        (element as HTMLElement).style.backgroundColor = 'rgba(255,182,216,0.3)';
        setTimeout(() => {
          (element as HTMLElement).style.backgroundColor = '';
        }, 2000);
      }
    }
  }

  private static getKeywordResponse(keyword: string): string {
    const responses: Record<string, string> = {
      skills: "Here are Vansh's key skills! He's particularly strong in Python, Machine Learning, and AI development.",
      projects: "Check out these featured projects! Each showcases different aspects of Vansh's technical expertise.",
      experience: "Here's Vansh's professional experience, including his current role as an AIML Development Engineer.",
      education: "Vansh is pursuing his B.Tech in Computer Science & Engineering at Guru Nanak Dev University.",
      contact: "Here's how you can get in touch with Vansh for opportunities or collaborations."
    };

    return responses[keyword] || `Here's information about ${keyword}.`;
  }
}

// Main conversation manager class
export class ConversationManager {
  private context: ConversationContext;
  private messageListeners: ((messages: ChatMessage[]) => void)[] = [];
  private statusListeners: ((status: MessageStatus | null) => void)[] = [];
  private autoSaveCleanup: (() => void) | null = null;

  constructor() {
    // Try to load recent conversation or create new one
    const recentConversation = ConversationPersistenceService.loadRecentConversation();
    this.context = recentConversation || this.createNewContext();

    // Enable auto-save if storage is available
    if (ConversationPersistenceService.isStorageAvailable()) {
      this.autoSaveCleanup = ConversationPersistenceService.enableAutoSave(this.context);
    }
  }

  private createNewContext(): ConversationContext {
    return {
      messages: [{
        id: generateId(),
        content: "Hi! I'm Nexus, Vansh's portfolio assistant. I can help you learn about his skills, projects, experience, and more! ✨",
        sender: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      }],
      sessionId: generateId(),
      startTime: new Date(),
      lastActivity: new Date(),
      portfolioFocus: []
    };
  }

  // Add message listeners
  onMessagesChange(listener: (messages: ChatMessage[]) => void): () => void {
    this.messageListeners.push(listener);
    return () => {
      const index = this.messageListeners.indexOf(listener);
      if (index > -1) {
        this.messageListeners.splice(index, 1);
      }
    };
  }

  onStatusChange(listener: (status: MessageStatus | null) => void): () => void {
    this.statusListeners.push(listener);
    return () => {
      const index = this.statusListeners.indexOf(listener);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  private notifyMessageListeners(): void {
    this.messageListeners.forEach(listener => listener(this.context.messages));
  }

  private notifyStatusListeners(status: MessageStatus | null): void {
    this.statusListeners.forEach(listener => listener(status));
  }

  // Send a message and get AI response with retry logic
  async sendMessage(content: string, retryCount: number = 0): Promise<void> {
    if (!content.trim()) return;

    console.log('💬 ConversationManager: Sending message:', content, `(attempt ${retryCount + 1})`);
    console.log('🔧 ConversationManager: AI Service available:', aiService.isAvailable());

    // Add user message only on first attempt
    if (retryCount === 0) {
      const userMessage: ChatMessage = {
        id: generateId(),
        content: content.trim(),
        sender: 'user',
        timestamp: new Date(),
        status: 'sent'
      };

      this.context.messages.push(userMessage);
      this.context.lastActivity = new Date();
      this.notifyMessageListeners();
    }

    // Show typing indicator
    this.notifyStatusListeners('typing');

    try {
      console.log('🤖 ConversationManager: Attempting AI response...');
      // Try to get AI response
      const response = await aiService.generateResponse(
        this.context.messages,
        this.context.portfolioFocus
      );

      console.log('✅ ConversationManager: AI response received:', response.content.substring(0, 100) + '...');

      // Add AI response
      const assistantMessage: ChatMessage = {
        id: generateId(),
        content: response.content,
        sender: 'assistant',
        timestamp: new Date(),
        status: 'sent',
        metadata: response.metadata
      };

      this.context.messages.push(assistantMessage);
      this.updatePortfolioFocus(content);
      
    } catch (error) {
      console.warn('⚠️ ConversationManager: AI service failed:', error, `(attempt ${retryCount + 1})`);

      // Check if we should retry
      if (ChatbotErrorHandler.shouldRetry(error as Error, retryCount)) {
        console.log(`🔄 ConversationManager: Retrying in ${ChatbotErrorHandler.getRetryDelay(retryCount)}ms...`);

        // Wait before retry
        setTimeout(() => {
          this.sendMessage(content, retryCount + 1);
        }, ChatbotErrorHandler.getRetryDelay(retryCount));

        return; // Don't show error message yet
      }

      // Use enhanced error handling for final failure
      const errorContext = {
        userMessage: content,
        timestamp: new Date(),
        sessionId: this.context.sessionId,
        retryCount,
        lastError: error as Error
      };

      const errorResponse = ChatbotErrorHandler.handleError(error as Error, errorContext);
      console.log('🔄 ConversationManager: Generated error response:', errorResponse.type);

      // Create user-friendly error message
      const errorMessage = ChatbotErrorHandler.createUserMessage(errorResponse, errorContext);
      this.context.messages.push(errorMessage);

      // If the error suggests fallback content, generate it
      if (errorResponse.type !== 'error') {
        const fallbackContent = EnhancedFallbackGenerator.generateResponse(content, errorContext);

        // Add fallback response as a separate message
        const fallbackMessage: ChatMessage = {
          id: generateId(),
          content: fallbackContent,
          sender: 'assistant',
          timestamp: new Date(),
          status: 'sent',
          metadata: {
            fallback: true,
            enhancedFallback: true,
            originalError: errorResponse.metadata,
            retryCount
          }
        };

        this.context.messages.push(fallbackMessage);
      }
    }

    this.context.lastActivity = new Date();
    this.notifyStatusListeners(null);
    this.notifyMessageListeners();
  }

  private updatePortfolioFocus(userMessage: string): void {
    // Use portfolio intelligence to analyze intent and update focus
    const analysis = PortfolioIntelligenceService.analyzeIntent(userMessage);

    if (analysis.confidence > 0.3) {
      const newFocus = analysis.intent;
      if (!this.context.portfolioFocus?.includes(newFocus)) {
        this.context.portfolioFocus = [...(this.context.portfolioFocus || []), newFocus];
      }
    }

    // Also add entities as focus areas
    analysis.entities.forEach(entity => {
      if (!this.context.portfolioFocus?.includes(entity)) {
        this.context.portfolioFocus = [...(this.context.portfolioFocus || []), entity];
      }
    });
  }

  // Get current messages
  getMessages(): ChatMessage[] {
    return [...this.context.messages];
  }

  // Get conversation context
  getContext(): ConversationContext {
    return { ...this.context };
  }

  // Clear conversation
  clearConversation(): void {
    // Save current conversation before clearing
    if (this.context.messages.length > 1) {
      ConversationPersistenceService.saveConversation(this.context);
    }

    this.context = this.createNewContext();
    this.notifyMessageListeners();
  }

  // Export conversation
  exportConversation(): string {
    return ConversationPersistenceService.exportConversation(this.context);
  }

  // Import conversation
  importConversation(jsonData: string): boolean {
    const imported = ConversationPersistenceService.importConversation(jsonData);
    if (imported) {
      this.context = imported;
      this.notifyMessageListeners();
      return true;
    }
    return false;
  }

  // Get conversation statistics
  getConversationStats() {
    return ConversationPersistenceService.getConversationStats();
  }

  // Cleanup method
  cleanup(): void {
    if (this.autoSaveCleanup) {
      this.autoSaveCleanup();
    }
  }

  // Check if AI is available
  isAIAvailable(): boolean {
    return aiService.isAvailable();
  }
}

// Export singleton instance
export const conversationManager = new ConversationManager();
