import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils';
import ChatMessage from '../../components/ui/ChatMessage';
import { ChatMessage as ChatMessageType } from '../../types';

describe('ChatMessage', () => {
  const mockUserMessage: ChatMessageType = {
    id: '1',
    content: 'Hello, how are you?',
    sender: 'user',
    timestamp: new Date('2024-01-01T10:00:00Z'),
    status: 'sent'
  };

  const mockAssistantMessage: ChatMessageType = {
    id: '2',
    content: 'Hi! I\'m doing well, thank you for asking.',
    sender: 'assistant',
    timestamp: new Date('2024-01-01T10:00:01Z'),
    status: 'sent'
  };

  const mockFormattedMessage: ChatMessageType = {
    id: '3',
    content: `**Hello!** This is a *formatted* message with:

• Bullet points
• **Bold text**
• *Italic text*

\`\`\`javascript
const code = "example";
\`\`\`

[Link example](https://example.com)`,
    sender: 'assistant',
    timestamp: new Date('2024-01-01T10:00:02Z'),
    status: 'sent'
  };

  describe('rendering', () => {
    it('should render user message correctly', () => {
      render(<ChatMessage message={mockUserMessage} />);
      
      expect(screen.getByText('Hello, how are you?')).toBeInTheDocument();
      expect(screen.getByLabelText('User')).toBeInTheDocument();
    });

    it('should render assistant message correctly', () => {
      render(<ChatMessage message={mockAssistantMessage} />);
      
      expect(screen.getByText('Hi! I\'m doing well, thank you for asking.')).toBeInTheDocument();
      expect(screen.getByLabelText('Assistant')).toBeInTheDocument();
    });

    it('should display timestamp correctly', () => {
      render(<ChatMessage message={mockUserMessage} />);
      
      // Should show formatted time
      expect(screen.getByText(/10:00/)).toBeInTheDocument();
    });

    it('should show message status icon', () => {
      const messageWithStatus = {
        ...mockUserMessage,
        status: 'sent' as const
      };
      
      render(<ChatMessage message={messageWithStatus} />);
      
      // Should have status indicator
      const statusIcon = screen.getByRole('img', { hidden: true });
      expect(statusIcon).toBeInTheDocument();
    });
  });

  describe('message formatting', () => {
    it('should format markdown content correctly', () => {
      render(<ChatMessage message={mockFormattedMessage} />);
      
      // Should render formatted content
      expect(screen.getByText('Hello!')).toBeInTheDocument();
      expect(screen.getByText('formatted')).toBeInTheDocument();
    });

    it('should handle code blocks', () => {
      const messageWithCode = {
        ...mockAssistantMessage,
        content: '```javascript\nconst test = "hello";\n```'
      };
      
      render(<ChatMessage message={messageWithCode} />);
      
      // Should render code block
      expect(screen.getByText('const test = "hello";')).toBeInTheDocument();
    });

    it('should handle bullet points', () => {
      const messageWithBullets = {
        ...mockAssistantMessage,
        content: '• First item\n• Second item\n• Third item'
      };
      
      render(<ChatMessage message={messageWithBullets} />);
      
      expect(screen.getByText('First item')).toBeInTheDocument();
      expect(screen.getByText('Second item')).toBeInTheDocument();
      expect(screen.getByText('Third item')).toBeInTheDocument();
    });

    it('should handle links correctly', () => {
      const messageWithLink = {
        ...mockAssistantMessage,
        content: 'Check out [this link](https://example.com) for more info.'
      };
      
      render(<ChatMessage message={messageWithLink} />);
      
      const link = screen.getByRole('link', { name: /this link/i });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'https://example.com');
      expect(link).toHaveAttribute('target', '_blank');
    });
  });

  describe('styling', () => {
    it('should apply correct styling for user messages', () => {
      render(<ChatMessage message={mockUserMessage} />);
      
      const messageContainer = screen.getByText('Hello, how are you?').closest('div');
      expect(messageContainer).toHaveClass('from-blue-600', 'to-purple-600');
    });

    it('should apply correct styling for assistant messages', () => {
      render(<ChatMessage message={mockAssistantMessage} />);
      
      const messageContainer = screen.getByText('Hi! I\'m doing well, thank you for asking.').closest('div');
      expect(messageContainer).toHaveClass('bg-white', 'dark:bg-gray-800');
    });

    it('should highlight latest message', () => {
      render(<ChatMessage message={mockUserMessage} isLatest={true} />);
      
      // Should have some indication it's the latest message
      const messageElement = screen.getByText('Hello, how are you?').closest('[data-testid]') || 
                           screen.getByText('Hello, how are you?').closest('div');
      expect(messageElement).toBeInTheDocument();
    });
  });

  describe('message status', () => {
    it('should show sent status correctly', () => {
      const sentMessage = {
        ...mockUserMessage,
        status: 'sent' as const
      };
      
      render(<ChatMessage message={sentMessage} />);
      
      // Should show checkmark or sent indicator
      expect(screen.getByLabelText(/sent/i)).toBeInTheDocument();
    });

    it('should show pending status correctly', () => {
      const pendingMessage = {
        ...mockUserMessage,
        status: 'pending' as const
      };
      
      render(<ChatMessage message={pendingMessage} />);
      
      // Should show pending indicator
      expect(screen.getByLabelText(/pending/i)).toBeInTheDocument();
    });

    it('should show failed status correctly', () => {
      const failedMessage = {
        ...mockUserMessage,
        status: 'failed' as const
      };
      
      render(<ChatMessage message={failedMessage} />);
      
      // Should show error indicator
      expect(screen.getByLabelText(/failed/i)).toBeInTheDocument();
    });
  });

  describe('metadata handling', () => {
    it('should handle message with metadata', () => {
      const messageWithMetadata = {
        ...mockAssistantMessage,
        metadata: {
          model: 'gemini-2.0-flash',
          processingTime: 150,
          fallback: false
        }
      };
      
      render(<ChatMessage message={messageWithMetadata} />);
      
      // Should render message normally
      expect(screen.getByText('Hi! I\'m doing well, thank you for asking.')).toBeInTheDocument();
    });

    it('should handle fallback message metadata', () => {
      const fallbackMessage = {
        ...mockAssistantMessage,
        metadata: {
          fallback: true,
          enhancedFallback: true
        }
      };
      
      render(<ChatMessage message={fallbackMessage} />);
      
      // Should render message (metadata doesn't affect display)
      expect(screen.getByText('Hi! I\'m doing well, thank you for asking.')).toBeInTheDocument();
    });

    it('should handle error message metadata', () => {
      const errorMessage = {
        ...mockAssistantMessage,
        content: 'An error occurred while processing your request.',
        metadata: {
          isError: true,
          errorType: 'warning',
          retryable: true
        }
      };
      
      render(<ChatMessage message={errorMessage} />);
      
      expect(screen.getByText('An error occurred while processing your request.')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<ChatMessage message={mockUserMessage} />);
      
      expect(screen.getByLabelText('User')).toBeInTheDocument();
    });

    it('should have proper ARIA labels for assistant', () => {
      render(<ChatMessage message={mockAssistantMessage} />);
      
      expect(screen.getByLabelText('Assistant')).toBeInTheDocument();
    });

    it('should have accessible timestamp', () => {
      render(<ChatMessage message={mockUserMessage} />);
      
      const timestamp = screen.getByText(/10:00/);
      expect(timestamp).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle empty message content', () => {
      const emptyMessage = {
        ...mockUserMessage,
        content: ''
      };
      
      render(<ChatMessage message={emptyMessage} />);
      
      // Should still render the message container
      expect(screen.getByLabelText('User')).toBeInTheDocument();
    });

    it('should handle very long messages', () => {
      const longMessage = {
        ...mockAssistantMessage,
        content: 'A'.repeat(1000)
      };
      
      render(<ChatMessage message={longMessage} />);
      
      expect(screen.getByText('A'.repeat(1000))).toBeInTheDocument();
    });

    it('should handle special characters', () => {
      const specialMessage = {
        ...mockAssistantMessage,
        content: 'Special chars: <>&"\'`~!@#$%^&*()[]{}|\\:";\'<>?,./'
      };
      
      render(<ChatMessage message={specialMessage} />);
      
      expect(screen.getByText(/Special chars:/)).toBeInTheDocument();
    });
  });
});
