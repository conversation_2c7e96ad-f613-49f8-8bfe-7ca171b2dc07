<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
  <defs>
    <radialGradient id="bright1" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#f472b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="bright2" cx="30%" cy="70%" r="60%">
      <stop offset="0%" style="stop-color:#22d3ee;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#60a5fa;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#a78bfa;stop-opacity:0.7" />
    </radialGradient>
    <radialGradient id="bright3" cx="70%" cy="30%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.8" />
      <stop offset="60%" style="stop-color:#f472b6;stop-opacity:0.7" />
      <stop offset="100%" style="stop-color:#a78bfa;stop-opacity:0.6" />
    </radialGradient>
    <filter id="softBlur">
      <feGaussianBlur in="SourceGraphic" stdDeviation="0.8"/>
    </filter>
  </defs>
  <circle cx="16" cy="16" r="16" fill="url(#bright1)"/>
  <path d="M0,16 Q6,8 16,10 Q26,12 32,6 Q30,22 20,20 Q10,24 4,22 Q0,20 0,16 Z" fill="url(#bright2)" filter="url(#softBlur)"/>
  <path d="M8,0 Q16,4 20,8 Q28,12 32,8 Q30,18 24,16 Q16,20 12,18 Q6,16 8,0 Z" fill="url(#bright3)" filter="url(#softBlur)"/>
  <path d="M16,32 Q24,28 28,24 Q32,20 28,16 Q24,12 16,14 Q8,16 4,20 Q0,24 16,32 Z" fill="url(#bright2)" filter="url(#softBlur)" opacity="0.6"/>
</svg>