# 🤖 Chatbot Setup Guide

## Overview
The portfolio chatbot (Nexus) can operate in two modes:
- **AI Mode**: Uses Google Gemini API for intelligent responses
- **Fallback Mode**: Uses predefined responses (works without API key)

## 🔧 Setup Instructions

### 1. Get Google Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 2. Set System Environment Variable
Set the `GEMINI_API_KEY` system environment variable:

**On Windows:**
```cmd
set GEMINI_API_KEY=your_actual_api_key_here
```

**On macOS/Linux:**
```bash
export GEMINI_API_KEY=your_actual_api_key_here
```

**Permanent Setup (recommended):**
Add to your shell profile (`.bashrc`, `.zshrc`, etc.):
```bash
export GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Restart Development Server
After adding the API key, restart your development server:
```bash
npm run dev
```

## 🎯 Features

### AI Mode (with API key)
- ✅ Intelligent responses using Google Gemini
- ✅ Context-aware conversations
- ✅ Advanced portfolio knowledge
- ✅ Dynamic response generation

### Fallback Mode (without API key)
- ✅ Predefined smart responses
- ✅ Project-specific information
- ✅ Navigation and highlighting
- ✅ Professional formatting

## 🔍 Verification

### Check Chatbot Status
1. Open the portfolio in your browser
2. Click the chatbot button (bottom-right)
3. Look at the header status:
   - **"AI Mode"** with green dot = API key working
   - **"Fallback Mode"** with yellow dot = No API key or API issues

### Test Functionality
Try these queries to test the chatbot:
- "Did you do any scraping projects?"
- "Tell me about your AI projects"
- "What are your main skills?"
- "Show me your work experience"

## 🛠️ Troubleshooting

### API Key Issues
- Ensure the API key is correctly set as system environment variable `GEMINI_API_KEY`
- Restart the development server after setting the environment variable
- Verify the API key is valid in Google AI Studio
- Check that the environment variable is accessible to the application

### Environment Variables
- Use system environment variable `GEMINI_API_KEY` (not `.env` file)
- Make sure the variable is set in the same terminal/session where you run the dev server
- For permanent setup, add to your shell profile

### Console Debugging
Open browser developer tools and check the console for:
- "Gemini API key loaded from system environment variable GEMINI_API_KEY" (success)
- "GEMINI_API_KEY not found in system environment variables" (missing key)
- Environment check details showing API key source
- Any API-related error messages

## 📝 Notes

- The chatbot works perfectly in fallback mode without an API key
- AI mode provides more dynamic and contextual responses
- All portfolio data is embedded in the application
- The chatbot automatically navigates to relevant sections
- Responses are formatted with professional styling

## 🔒 Security

- Never commit your actual API key to version control
- The `.env` file is automatically ignored by git
- API keys are only used client-side for this demo
- For production, consider server-side API calls
