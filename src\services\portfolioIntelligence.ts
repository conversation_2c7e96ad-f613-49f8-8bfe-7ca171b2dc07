import { ChatMessage, PortfolioData } from '@/types';
import portfolioData from '@/data/portfolio';

// Portfolio Intelligence Service for smart, context-aware responses
export class PortfolioIntelligenceService {
  
  // Analyze user intent from their message
  static analyzeIntent(message: string): {
    intent: string;
    entities: string[];
    confidence: number;
  } {
    const lowerMessage = message.toLowerCase();
    
    // Define intent patterns
    const intentPatterns = {
      skills: ['skill', 'technology', 'tech', 'programming', 'language', 'framework', 'tool'],
      projects: ['project', 'work', 'built', 'created', 'developed', 'portfolio'],
      experience: ['experience', 'job', 'work', 'career', 'professional', 'intern'],
      education: ['education', 'degree', 'university', 'college', 'study', 'learn'],
      contact: ['contact', 'reach', 'email', 'phone', 'connect', 'hire'],
      about: ['about', 'who', 'background', 'story', 'bio'],
      specific_tech: ['python', 'ai', 'ml', 'machine learning', 'django', 'aws', 'cloud', 'tensorflow'],
      specific_project: ['promptwizard', 'medical', 'jute', 'sports', 'house price', 'tips'],
      comparison: ['vs', 'versus', 'compare', 'difference', 'better'],
      recommendation: ['recommend', 'suggest', 'advice', 'should', 'best']
    };

    let bestIntent = 'general';
    let maxScore = 0;
    const entities: string[] = [];

    // Score each intent
    for (const [intent, keywords] of Object.entries(intentPatterns)) {
      let score = 0;
      for (const keyword of keywords) {
        if (lowerMessage.includes(keyword)) {
          score += 1;
          entities.push(keyword);
        }
      }
      
      if (score > maxScore) {
        maxScore = score;
        bestIntent = intent;
      }
    }

    return {
      intent: bestIntent,
      entities: [...new Set(entities)], // Remove duplicates
      confidence: Math.min(maxScore / 3, 1) // Normalize to 0-1
    };
  }

  // Extract relevant portfolio sections based on intent
  static getRelevantPortfolioData(intent: string, entities: string[]): any {
    const data = portfolioData;
    
    switch (intent) {
      case 'skills':
        return {
          skills: data.skills,
          technologies: data.technologies,
          relevantProjects: data.projects.filter(p => 
            entities.some(entity => 
              p.description.toLowerCase().includes(entity) ||
              p.technologies.some(tech => tech.name.toLowerCase().includes(entity))
            )
          )
        };
        
      case 'projects':
        return {
          projects: data.projects,
          skills: data.skills.filter(s => s.category === 'Programming' || s.category === 'AI-ML'),
          experience: data.experience
        };
        
      case 'experience':
        return {
          experience: data.experience,
          leadership: data.leadership,
          skills: data.skills,
          relevantProjects: data.projects.filter(p => p.featured)
        };
        
      case 'education':
        return {
          education: data.education,
          leadership: data.leadership,
          skills: data.skills.filter(s => s.level > 80)
        };
        
      case 'contact':
        return {
          contact: data.personal.contact,
          personal: {
            name: data.personal.name,
            title: data.personal.title
          }
        };
        
      case 'specific_tech':
        const techEntity = entities.find(e => 
          ['python', 'ai', 'ml', 'machine learning', 'django', 'aws', 'cloud', 'tensorflow'].includes(e)
        );
        return {
          skills: data.skills.filter(s => 
            s.name.toLowerCase().includes(techEntity || '') ||
            s.category?.toLowerCase().includes(techEntity || '')
          ),
          projects: data.projects.filter(p =>
            p.description.toLowerCase().includes(techEntity || '') ||
            p.technologies.some(tech => tech.name.toLowerCase().includes(techEntity || ''))
          ),
          experience: data.experience
        };
        
      case 'specific_project':
        const projectEntity = entities.find(e => 
          ['promptwizard', 'medical', 'jute', 'sports', 'house', 'tips'].includes(e)
        );
        return {
          projects: data.projects.filter(p =>
            p.title.toLowerCase().includes(projectEntity || '') ||
            p.description.toLowerCase().includes(projectEntity || '')
          ),
          skills: data.skills,
          experience: data.experience
        };
        
      default:
        return {
          personal: data.personal,
          skills: data.skills.slice(0, 5), // Top 5 skills
          projects: data.projects.filter(p => p.featured),
          experience: data.experience
        };
    }
  }

  // Generate contextual follow-up questions
  static generateFollowUpQuestions(intent: string, entities: string[]): string[] {
    const followUps: Record<string, string[]> = {
      skills: [
        "Which programming languages are you most proficient in?",
        "What AI/ML frameworks do you prefer?",
        "How do you stay updated with new technologies?",
        "What's your experience with cloud platforms?"
      ],
      projects: [
        "What was the most challenging project you've worked on?",
        "Which project are you most proud of?",
        "How do you approach problem-solving in your projects?",
        "What technologies did you learn while building these projects?"
      ],
      experience: [
        "What do you enjoy most about your current role?",
        "How has your experience shaped your technical skills?",
        "What kind of projects do you work on at EaseMyMed?",
        "What are your career goals?"
      ],
      education: [
        "What subjects interest you most in your CS program?",
        "How do you balance academics with practical projects?",
        "What programming concepts did you find most challenging?",
        "Are you involved in any tech communities at university?"
      ],
      specific_tech: [
        "What's your favorite Python library?",
        "How do you approach machine learning projects?",
        "What cloud services do you use most?",
        "What's your experience with AI model deployment?"
      ]
    };

    return followUps[intent] || [
      "What would you like to know more about?",
      "Is there a specific area you'd like to explore?",
      "Would you like to see some project examples?",
      "How can I help you learn more about this portfolio?"
    ];
  }

  // Analyze conversation patterns to suggest next topics
  static analyzeConversationFlow(messages: ChatMessage[]): {
    suggestedTopics: string[];
    conversationDepth: number;
    userInterests: string[];
  } {
    const userMessages = messages.filter(m => m.sender === 'user');
    const topics = userMessages.map(m => this.analyzeIntent(m.content).intent);
    const allEntities = userMessages.flatMap(m => this.analyzeIntent(m.content).entities);
    
    // Count topic frequency
    const topicCounts = topics.reduce((acc, topic) => {
      acc[topic] = (acc[topic] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Determine conversation depth
    const conversationDepth = Math.min(userMessages.length / 3, 1);

    // Suggest unexplored topics
    const allTopics = ['skills', 'projects', 'experience', 'education', 'contact'];
    const exploredTopics = Object.keys(topicCounts);
    const suggestedTopics = allTopics.filter(topic => !exploredTopics.includes(topic));

    return {
      suggestedTopics,
      conversationDepth,
      userInterests: [...new Set(allEntities)]
    };
  }

  // Generate smart response suggestions based on context
  static generateSmartSuggestions(
    messages: ChatMessage[],
    currentIntent?: string
  ): string[] {
    const analysis = this.analyzeConversationFlow(messages);
    
    // If conversation is new, suggest broad topics
    if (messages.length <= 2) {
      return [
        "What are your main technical skills?",
        "Tell me about your featured projects",
        "What's your professional experience?",
        "How can I get in touch with you?"
      ];
    }

    // If user has shown interest in specific areas, dive deeper
    if (analysis.userInterests.length > 0) {
      const interests = analysis.userInterests;
      const suggestions: string[] = [];

      if (interests.some(i => ['python', 'programming', 'code'].includes(i))) {
        suggestions.push("What's your favorite Python project?");
        suggestions.push("How do you approach code quality?");
      }

      if (interests.some(i => ['ai', 'ml', 'machine learning'].includes(i))) {
        suggestions.push("What AI models have you implemented?");
        suggestions.push("Tell me about your RAG implementation");
      }

      if (interests.some(i => ['project', 'work', 'built'].includes(i))) {
        suggestions.push("What was your most challenging project?");
        suggestions.push("How do you choose technologies for projects?");
      }

      return suggestions.slice(0, 4);
    }

    // Suggest unexplored areas
    return analysis.suggestedTopics.map(topic => {
      switch (topic) {
        case 'skills': return "What technologies do you work with?";
        case 'projects': return "Show me your project portfolio";
        case 'experience': return "Tell me about your work experience";
        case 'education': return "What's your educational background?";
        case 'contact': return "How can I reach out to you?";
        default: return "What else would you like to know?";
      }
    }).slice(0, 4);
  }
}

export default PortfolioIntelligenceService;
