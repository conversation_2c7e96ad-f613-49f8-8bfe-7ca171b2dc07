// Define action types as constants
const ACTION_NAVIGATE = "navigate";
const ACTION_HIGHLIGHT = "highlight";

/**
 * Website keyword-to-action index for navigation, highlighting, and contextual actions.
 * Each entry maps a keyword or phrase to a UI action, target element, and scroll behavior.
 * Extend this object as needed for new keywords and UI features.
 */
const websiteIndex = {
  // Navigation keywords
  about: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  summary: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  profile: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  skills: { action: ACTION_NAVIGATE, target: "#skills-section", scroll: true },
  technologies: { action: ACTION_NAVIGATE, target: "#skills-section", scroll: true },
  tools: { action: ACTION_NAVIGATE, target: "#skills-section", scroll: true },
  projects: { action: ACTION_NAVIGATE, target: "#projects-section", scroll: true },
  experience: { action: ACTION_NAVIGATE, target: "#experience-section", scroll: true },
  workExperience: { action: ACTION_NAVIGATE, target: "#experience-section", scroll: true },
  education: { action: ACTION_NAVIGATE, target: "#education-section", scroll: true },
  academics: { action: ACTION_NAVIGATE, target: "#education-section", scroll: true },
  leadership: { action: ACTION_NAVIGATE, target: "#leadership-section", scroll: true },
  volunteer: { action: ACTION_NAVIGATE, target: "#leadership-section", scroll: true },
  testimonials: { action: ACTION_NAVIGATE, target: "#testimonials-section", scroll: true },
  references: { action: ACTION_NAVIGATE, target: "#testimonials-section", scroll: true },
  contact: { action: ACTION_NAVIGATE, target: "#contact-section", scroll: true },
  address: { action: ACTION_NAVIGATE, target: "#contact-section", scroll: true },
  phone: { action: ACTION_NAVIGATE, target: "#contact-section", scroll: true },
  email: { action: ACTION_NAVIGATE, target: "#contact-section", scroll: true },
  socials: { action: ACTION_NAVIGATE, target: "#contact-section", scroll: true },

  // Social Media Highlighting
  linkedin: { action: ACTION_HIGHLIGHT, target: "#social-linkedin", scroll: false },
  github: { action: ACTION_HIGHLIGHT, target: "#social-github", scroll: false },
  facebook: { action: ACTION_HIGHLIGHT, target: "#social-facebook", scroll: false },
  twitter: { action: ACTION_HIGHLIGHT, target: "#social-twitter", scroll: false },
  instagram: { action: ACTION_HIGHLIGHT, target: "#social-instagram", scroll: false },

  // Skill/Tech highlighting
  python: { action: ACTION_HIGHLIGHT, target: "#skill-python", scroll: false },
  cplusplus: { action: ACTION_HIGHLIGHT, target: "#skill-cpp", scroll: false },
  c: { action: ACTION_HIGHLIGHT, target: "#skill-c", scroll: false },
  html: { action: ACTION_HIGHLIGHT, target: "#skill-html-css", scroll: false },
  css: { action: ACTION_HIGHLIGHT, target: "#skill-html-css", scroll: false },
  sql: { action: ACTION_HIGHLIGHT, target: "#skill-sql", scroll: false },
  cypher: { action: ACTION_HIGHLIGHT, target: "#skill-cypher", scroll: false },
  machineLearning: { action: ACTION_HIGHLIGHT, target: "#skill-machine-learning", scroll: false },
  ml: { action: ACTION_HIGHLIGHT, target: "#skill-machine-learning", scroll: false },
  largeLanguageModels: { action: ACTION_HIGHLIGHT, target: "#skill-large-language-models", scroll: false },
  llm: { action: ACTION_HIGHLIGHT, target: "#skill-large-language-models", scroll: false },
  tensorflow: { action: ACTION_HIGHLIGHT, target: "#skill-tensorflow", scroll: false },
  opencv: { action: ACTION_HIGHLIGHT, target: "#skill-opencv", scroll: false },
  django: { action: ACTION_HIGHLIGHT, target: "#skill-django", scroll: false },
  scikitLearn: { action: ACTION_HIGHLIGHT, target: "#skill-scikit-learn", scroll: false },
  sagemaker: { action: ACTION_HIGHLIGHT, target: "#skill-aws-sagemaker", scroll: false },
  awsSagemaker: { action: ACTION_HIGHLIGHT, target: "#skill-aws-sagemaker", scroll: false },
  googleCloud: { action: ACTION_HIGHLIGHT, target: "#skill-google-cloud-platform", scroll: false },
  gcp: { action: ACTION_HIGHLIGHT, target: "#skill-google-cloud-platform", scroll: false },
  docker: { action: ACTION_HIGHLIGHT, target: "#skill-docker", scroll: false },
  git: { action: ACTION_HIGHLIGHT, target: "#skill-git", scroll: false },
  githubCopilot: { action: ACTION_HIGHLIGHT, target: "#skill-github-copilot", scroll: false },
  documentation: { action: ACTION_HIGHLIGHT, target: "#skill-documentation", scroll: false },
  research: { action: ACTION_HIGHLIGHT, target: "#skill-research", scroll: false },
  cloud: { action: ACTION_HIGHLIGHT, target: "#skills-cloud", scroll: false },
  devops: { action: ACTION_HIGHLIGHT, target: "#skills-devops", scroll: false },
  data: { action: ACTION_HIGHLIGHT, target: "#skills-data", scroll: false },
  ai: { action: ACTION_HIGHLIGHT, target: "#skills-ai-ml", scroll: false },
  mlops: { action: ACTION_HIGHLIGHT, target: "#skills-mlops", scroll: false },
  notion: { action: ACTION_HIGHLIGHT, target: "#tech-notion", scroll: false },
  vsCode: { action: ACTION_HIGHLIGHT, target: "#tech-vs-code", scroll: false },
  vim: { action: ACTION_HIGHLIGHT, target: "#tech-vim", scroll: false },
  androidStudio: { action: ACTION_HIGHLIGHT, target: "#tech-android-studio", scroll: false },
  ec2: { action: ACTION_HIGHLIGHT, target: "#tech-aws-ec2", scroll: false },
  s3: { action: ACTION_HIGHLIGHT, target: "#tech-s3-buckets", scroll: false },
  linux: { action: ACTION_HIGHLIGHT, target: "#tech-linux", scroll: false },
  googleColab: { action: ACTION_HIGHLIGHT, target: "#tech-google-colab", scroll: false },
  graphdb: { action: ACTION_HIGHLIGHT, target: "#tech-graphdb", scroll: false },
  graphrag: { action: ACTION_HIGHLIGHT, target: "#tech-graphrag", scroll: false },
  datastax: { action: ACTION_HIGHLIGHT, target: "#tech-datastax", scroll: false },
  makeCom: { action: ACTION_HIGHLIGHT, target: "#tech-make-com", scroll: false },
  langgraph: { action: ACTION_HIGHLIGHT, target: "#tech-langgraph", scroll: false },
  ollama: { action: ACTION_HIGHLIGHT, target: "#tech-ollama", scroll: false },
  openai: { action: ACTION_HIGHLIGHT, target: "#tech-openai", scroll: false },
  gemini: { action: ACTION_HIGHLIGHT, target: "#tech-gemini", scroll: false },
  deepseek: { action: ACTION_HIGHLIGHT, target: "#tech-deepseek", scroll: false },
  roocode: { action: ACTION_HIGHLIGHT, target: "#tech-roocode", scroll: false },
  amazonQDev: { action: ACTION_HIGHLIGHT, target: "#tech-amazon-q-dev", scroll: false },
  cursorai: { action: ACTION_HIGHLIGHT, target: "#tech-cursorai", scroll: false },
  napkinAi: { action: ACTION_HIGHLIGHT, target: "#tech-napkin-ai", scroll: false },
  drawIo: { action: ACTION_HIGHLIGHT, target: "#tech-draw-io", scroll: false },
  multiprocessing: { action: ACTION_HIGHLIGHT, target: "#tech-multiprocessing", scroll: false },
  threads: { action: ACTION_HIGHLIGHT, target: "#tech-threads", scroll: false },
  streamlit: { action: ACTION_HIGHLIGHT, target: "#tech-streamlit", scroll: false },
  pygame: { action: ACTION_HIGHLIGHT, target: "#tech-pygame", scroll: false },
  nltk: { action: ACTION_HIGHLIGHT, target: "#tech-nltk", scroll: false },
  pandas: { action: ACTION_HIGHLIGHT, target: "#tech-pandas", scroll: false },
  numpy: { action: ACTION_HIGHLIGHT, target: "#tech-numpy", scroll: false },
  matplotlib: { action: ACTION_HIGHLIGHT, target: "#tech-matplotlib", scroll: false },
  seaborn: { action: ACTION_HIGHLIGHT, target: "#tech-seaborn", scroll: false },
  neo4j: { action: ACTION_HIGHLIGHT, target: "#tech-neo4j", scroll: false },
  bhashiniAi: { action: ACTION_HIGHLIGHT, target: "#tech-bhashini-ai", scroll: false },
  selenium: { action: ACTION_HIGHLIGHT, target: "#tech-selenium", scroll: false },
  ciCd: { action: ACTION_HIGHLIGHT, target: "#tech-ci-cd", scroll: false },
  notebookllm: { action: ACTION_HIGHLIGHT, target: "#tech-notebookllm", scroll: false },
  chatgpt: { action: ACTION_HIGHLIGHT, target: "#tech-chatgpt", scroll: false },
  claude: { action: ACTION_HIGHLIGHT, target: "#tech-claude", scroll: false },

  // Project-specific keywords
  promptwizard: { action: ACTION_NAVIGATE, target: "#project-promptwizard", scroll: true },
  promptWizard: { action: ACTION_NAVIGATE, target: "#project-promptwizard", scroll: true },
  drMedicinePrescriptionPrediction: { action: ACTION_NAVIGATE, target: "#project-prescription", scroll: true },
  prescriptionPrediction: { action: ACTION_NAVIGATE, target: "#project-prescription", scroll: true },
  jutePestClassification: { action: ACTION_NAVIGATE, target: "#project-jute-pest", scroll: true },
  jutePest: { action: ACTION_NAVIGATE, target: "#project-jute-pest", scroll: true },
  sportsPersonClassification: { action: ACTION_NAVIGATE, target: "#project-sports-person", scroll: true },
  sportsPersonClassifier: { action: ACTION_NAVIGATE, target: "#project-sports-person", scroll: true },
  bombayHousePricePrediction: { action: ACTION_NAVIGATE, target: "#project-bhp", scroll: true },
  housePricePrediction: { action: ACTION_NAVIGATE, target: "#project-bhp", scroll: true },
  tipsApp: { action: ACTION_NAVIGATE, target: "#project-tips-app", scroll: true },
  officialSiteLinkScrapingFromName: { action: ACTION_NAVIGATE, target: "#project-site-link-scraping", scroll: true },
  siteLinkScraping: { action: ACTION_NAVIGATE, target: "#project-site-link-scraping", scroll: true },
  pongGame: { action: ACTION_NAVIGATE, target: "#project-pong-game", scroll: true },
  scrapAndSentimentalAnalysis: { action: ACTION_NAVIGATE, target: "#project-blackcoffer", scroll: true },
  sentimentalAnalysis: { action: ACTION_NAVIGATE, target: "#project-blackcoffer", scroll: true },
  blackcoffer: { action: ACTION_NAVIGATE, target: "#project-blackcoffer", scroll: true },

  // Experience/leadership/volunteer
  easymymed: { action: ACTION_NAVIGATE, target: "#experience-easymymed", scroll: true },
  aimlDevelopmentEngineerIntern: { action: ACTION_NAVIGATE, target: "#experience-easymymed", scroll: true },
  neo4jWorkshops: { action: ACTION_NAVIGATE, target: "#leadership-neo4j", scroll: true },
  graphDatabaseResearch: { action: ACTION_NAVIGATE, target: "#leadership-neo4j", scroll: true },
  gnduEcell: { action: ACTION_NAVIGATE, target: "#leadership-gndu-ecell", scroll: true },
  designTeamHead: { action: ACTION_NAVIGATE, target: "#leadership-gndu-ecell", scroll: true },
  arambhStartup: { action: ACTION_NAVIGATE, target: "#leadership-arambh", scroll: true },
  backendTeamTrainee: { action: ACTION_NAVIGATE, target: "#leadership-arambh", scroll: true },
  hamariPahchanNgo: { action: ACTION_NAVIGATE, target: "#leadership-hamari-pahchan", scroll: true },
  charityDrive: { action: ACTION_NAVIGATE, target: "#leadership-hamari-pahchan", scroll: true },
  corizoCompany: { action: ACTION_NAVIGATE, target: "#leadership-corizo", scroll: true },
  marketingIntern: { action: ACTION_NAVIGATE, target: "#leadership-corizo", scroll: true },

  // Education
  guruNanakDevUniversity: { action: ACTION_NAVIGATE, target: "#education-gndu", scroll: true },
  bachelorOfTechnology: { action: ACTION_NAVIGATE, target: "#education-gndu", scroll: true },
  montgomeryGuruNanakPublicSchool: { action: ACTION_NAVIGATE, target: "#education-montgomery", scroll: true },
  littleAnglesCoEdPublicSchool: { action: ACTION_NAVIGATE, target: "#education-little-angels", scroll: true },

  // Testimonials
  nikhil: { action: ACTION_HIGHLIGHT, target: "#testimonial-nikhil", scroll: true },
  harshraj: { action: ACTION_HIGHLIGHT, target: "#testimonial-harshraj", scroll: true },
  tarun: { action: ACTION_HIGHLIGHT, target: "#testimonial-tarun", scroll: true },

  // Miscellaneous
  resume: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  portfolio: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  home: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  main: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
  start: { action: ACTION_NAVIGATE, target: "#about-section", scroll: true },
};

export default websiteIndex;
