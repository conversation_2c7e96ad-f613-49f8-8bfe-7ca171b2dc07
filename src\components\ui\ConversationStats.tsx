import React from 'react';
import { ConversationContext } from '@/types';
import { <PERSON>, MessageSquare, Z<PERSON>, Brain } from 'lucide-react';

interface ConversationStatsProps {
  context: ConversationContext;
  isVisible: boolean;
}

const ConversationStats: React.FC<ConversationStatsProps> = ({ context, isVisible }) => {
  if (!isVisible) return null;

  const formatDuration = (startTime: Date, endTime: Date): string => {
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    
    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs}s`;
    }
    return `${diffSecs}s`;
  };

  const getAIResponseCount = (): number => {
    return context.messages.filter(m => m.sender === 'assistant').length;
  };

  const getAverageResponseTime = (): number => {
    const aiMessages = context.messages.filter(m => m.sender === 'assistant' && m.metadata?.processingTime);
    if (aiMessages.length === 0) return 0;
    
    const totalTime = aiMessages.reduce((sum, msg) => sum + (msg.metadata?.processingTime || 0), 0);
    return Math.round(totalTime / aiMessages.length);
  };

  const getFocusAreas = (): string[] => {
    return context.portfolioFocus || [];
  };

  const stats = [
    {
      icon: <Clock className="w-3 h-3" />,
      label: "Duration",
      value: formatDuration(context.startTime, context.lastActivity),
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: <MessageSquare className="w-3 h-3" />,
      label: "Messages",
      value: context.messages.length.toString(),
      color: "text-green-600 dark:text-green-400"
    },
    {
      icon: <Zap className="w-3 h-3" />,
      label: "Avg Response",
      value: getAverageResponseTime() > 0 ? `${getAverageResponseTime()}ms` : "N/A",
      color: "text-yellow-600 dark:text-yellow-400"
    },
    {
      icon: <Brain className="w-3 h-3" />,
      label: "AI Responses",
      value: getAIResponseCount().toString(),
      color: "text-purple-600 dark:text-purple-400"
    }
  ];

  const focusAreas = getFocusAreas();

  return (
    <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
      <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-medium">
        Conversation Stats
      </div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-2 mb-3">
        {stats.map((stat, index) => (
          <div key={index} className="flex items-center gap-2">
            <span className={stat.color}>
              {stat.icon}
            </span>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {stat.label}
              </span>
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                {stat.value}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Focus Areas */}
      {focusAreas.length > 0 && (
        <div>
          <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
            Topics Discussed
          </div>
          <div className="flex flex-wrap gap-1">
            {focusAreas.map((area, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full"
              >
                {area}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Session Info */}
      <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Session: {context.sessionId.slice(-8)}
        </div>
      </div>
    </div>
  );
};

export default ConversationStats;
