<!-- Portfolio Content -->
<div class="portfolio-content">
    <h1><PERSON><PERSON></h1>
    <h2>AIML Development Engineer</h2>
    <!-- ...other portfolio sections... -->
</div>

<!-- Chatbot UI -->
<div class="nexus-chat">
    <div class="nexus-icon" onclick="toggleChat()">
        <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
        </svg>
    </div>
</div>

<div class="nexus-panel" id="chatPanel">
    <div class="chat-header">
        <h3>Nexus Assistant</h3>
        <button onclick="toggleChat()" style="background: none; color: var(--accent); padding: 5px;">✕</button>
    </div>
    <div id="maintenanceNotice" class="maintenance-notice" style="display: none;">
        AI service is currently in maintenance. Using keyword matching instead.
    </div>
    <div class="chat-messages" id="chatMessages"></div>
    <div class="chat-input">
        <input type="text" id="userInput" placeholder="Ask about the portfolio...">
        <button onclick="sendMessage()">Send</button>
    </div>
</div>

<!-- Script Imports for Chatbot -->
<script src="https://unpkg.com/@google/generative-ai@0.1.0/dist/index.js"></script>
<script src="AIIntegration.js"></script>
<script src="websiteIndex.js"></script>
<script src="Chatbot.js"></script>
<script src="portfolioData.js"></script>
<script src="styles.js"></script>
