import { ConversationContext, ChatMessage } from '@/types';

// Conversation Persistence Service for saving and restoring chat history
export class ConversationPersistenceService {
  private static readonly STORAGE_KEY = 'nexus_conversation_history';
  private static readonly MAX_STORED_CONVERSATIONS = 5;
  private static readonly MAX_CONVERSATION_AGE_DAYS = 7;

  // Save conversation to localStorage
  static saveConversation(context: ConversationContext): void {
    try {
      const conversations = this.getStoredConversations();
      
      // Add current conversation
      const conversationData = {
        ...context,
        savedAt: new Date().toISOString()
      };
      
      conversations.unshift(conversationData);
      
      // Keep only recent conversations
      const filteredConversations = conversations
        .slice(0, this.MAX_STORED_CONVERSATIONS)
        .filter(conv => this.isConversationRecent(conv.savedAt));
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredConversations));
    } catch (error) {
      console.warn('Failed to save conversation:', error);
    }
  }

  // Load the most recent conversation
  static loadRecentConversation(): ConversationContext | null {
    try {
      const conversations = this.getStoredConversations();
      
      if (conversations.length === 0) {
        return null;
      }
      
      const recent = conversations[0];
      
      // Convert date strings back to Date objects
      return {
        ...recent,
        startTime: new Date(recent.startTime),
        lastActivity: new Date(recent.lastActivity),
        messages: recent.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      };
    } catch (error) {
      console.warn('Failed to load conversation:', error);
      return null;
    }
  }

  // Get all stored conversations
  static getStoredConversations(): any[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to parse stored conversations:', error);
      return [];
    }
  }

  // Check if conversation is recent enough to keep
  private static isConversationRecent(savedAt: string): boolean {
    const saved = new Date(savedAt);
    const now = new Date();
    const daysDiff = (now.getTime() - saved.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= this.MAX_CONVERSATION_AGE_DAYS;
  }

  // Clear all stored conversations
  static clearStoredConversations(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear conversations:', error);
    }
  }

  // Export conversation as JSON
  static exportConversation(context: ConversationContext): string {
    const exportData = {
      ...context,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  // Import conversation from JSON
  static importConversation(jsonData: string): ConversationContext | null {
    try {
      const data = JSON.parse(jsonData);
      
      // Validate the data structure
      if (!data.sessionId || !data.messages || !Array.isArray(data.messages)) {
        throw new Error('Invalid conversation format');
      }
      
      return {
        ...data,
        startTime: new Date(data.startTime),
        lastActivity: new Date(data.lastActivity),
        messages: data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      };
    } catch (error) {
      console.warn('Failed to import conversation:', error);
      return null;
    }
  }

  // Get conversation statistics
  static getConversationStats(): {
    totalConversations: number;
    totalMessages: number;
    averageMessagesPerConversation: number;
    oldestConversation: string | null;
    newestConversation: string | null;
  } {
    const conversations = this.getStoredConversations();
    
    if (conversations.length === 0) {
      return {
        totalConversations: 0,
        totalMessages: 0,
        averageMessagesPerConversation: 0,
        oldestConversation: null,
        newestConversation: null
      };
    }
    
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
    const dates = conversations.map(conv => conv.savedAt).sort();
    
    return {
      totalConversations: conversations.length,
      totalMessages,
      averageMessagesPerConversation: Math.round(totalMessages / conversations.length),
      oldestConversation: dates[0],
      newestConversation: dates[dates.length - 1]
    };
  }

  // Auto-save functionality
  static enableAutoSave(context: ConversationContext, intervalMs: number = 30000): () => void {
    const interval = setInterval(() => {
      // Only save if there are user messages and recent activity
      const hasUserMessages = context.messages.some(m => m.sender === 'user');
      const recentActivity = Date.now() - context.lastActivity.getTime() < 300000; // 5 minutes
      
      if (hasUserMessages && recentActivity) {
        this.saveConversation(context);
      }
    }, intervalMs);
    
    // Return cleanup function
    return () => clearInterval(interval);
  }

  // Check if browser supports localStorage
  static isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  // Get storage usage information
  static getStorageInfo(): {
    isAvailable: boolean;
    usedSpace: number;
    conversationData: number;
  } {
    if (!this.isStorageAvailable()) {
      return {
        isAvailable: false,
        usedSpace: 0,
        conversationData: 0
      };
    }
    
    const conversationData = localStorage.getItem(this.STORAGE_KEY);
    const conversationSize = conversationData ? new Blob([conversationData]).size : 0;
    
    // Estimate total localStorage usage
    let totalSize = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length + key.length;
      }
    }
    
    return {
      isAvailable: true,
      usedSpace: totalSize,
      conversationData: conversationSize
    };
  }
}

export default ConversationPersistenceService;
