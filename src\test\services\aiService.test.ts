import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AIService, AIServiceError } from '../../services/aiService';
import { mockEnvironment, mockApiResponses } from '../utils';

describe('AIService', () => {
  let aiService: AIService;
  let restoreEnv: () => void;

  beforeEach(() => {
    aiService = new AIService();
    restoreEnv = mockEnvironment();
  });

  afterEach(() => {
    restoreEnv();
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully with API key', async () => {
      restoreEnv();
      restoreEnv = mockEnvironment({ GEMINI_API_KEY: 'test-api-key-123' });

      await aiService.initialize();

      expect(aiService.isAvailable()).toBe(true);
    });

    it('should handle missing API key gracefully', async () => {
      restoreEnv();
      restoreEnv = mockEnvironment({ GEMINI_API_KEY: undefined });

      await aiService.initialize();

      expect(aiService.isAvailable()).toBe(true); // Should still be available for fallback
    });

    it('should detect environment variables correctly', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      await aiService.initialize();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Environment Analysis:')
      );
      
      consoleSpy.mockRestore();
    });

    it('should validate API key format', async () => {
      restoreEnv();
      restoreEnv = mockEnvironment({ GEMINI_API_KEY: 'invalid-key' });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      await aiService.initialize();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('API key format may be invalid')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('generateResponse', () => {
    beforeEach(async () => {
      restoreEnv();
      restoreEnv = mockEnvironment({ GEMINI_API_KEY: 'AIzaSyTest123456789012345678901234567890' });
      await aiService.initialize();
    });

    it('should generate response successfully', async () => {
      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      const response = await aiService.generateResponse(mockMessages);

      expect(response).toHaveProperty('content');
      expect(response).toHaveProperty('metadata');
      expect(typeof response.content).toBe('string');
      expect(response.content.length).toBeGreaterThan(0);
    });

    it('should handle rate limiting', async () => {
      // Mock rate limit exceeded
      const mockGenerateContent = vi.fn().mockRejectedValue(
        new Error('Rate limit exceeded')
      );
      
      (window as any).generativeai.Defaults = vi.fn().mockImplementation(() => ({
        getGenerativeModel: vi.fn().mockReturnValue({
          generateContent: mockGenerateContent
        })
      }));

      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      await expect(aiService.generateResponse(mockMessages)).rejects.toThrow(
        AIServiceError
      );
    });

    it('should handle missing API key error', async () => {
      restoreEnv();
      restoreEnv = mockEnvironment({ GEMINI_API_KEY: undefined });
      
      const aiServiceNoKey = new AIService();
      await aiServiceNoKey.initialize();

      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      await expect(aiServiceNoKey.generateResponse(mockMessages)).rejects.toThrow(
        'API key not available'
      );
    });

    it('should handle SDK not loaded error', async () => {
      // Remove the mock SDK
      delete (window as any).generativeai;

      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      await expect(aiService.generateResponse(mockMessages)).rejects.toThrow(
        'Google Generative AI not loaded'
      );
    });
  });

  describe('error handling', () => {
    it('should create AIServiceError with correct properties', () => {
      const error = new AIServiceError(
        'Test error',
        'TEST_ERROR',
        'test-provider',
        true
      );

      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.provider).toBe('test-provider');
      expect(error.retryable).toBe(true);
      expect(error.name).toBe('AIServiceError');
    });

    it('should handle network errors gracefully', async () => {
      const mockGenerateContent = vi.fn().mockRejectedValue(
        new Error('Network error')
      );
      
      (window as any).generativeai.Defaults = vi.fn().mockImplementation(() => ({
        getGenerativeModel: vi.fn().mockReturnValue({
          generateContent: mockGenerateContent
        })
      }));

      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      await expect(aiService.generateResponse(mockMessages)).rejects.toThrow();
    });
  });

  describe('provider management', () => {
    it('should return available providers', () => {
      const providers = aiService.getAvailableProviders();
      
      expect(Array.isArray(providers)).toBe(true);
      expect(providers.length).toBeGreaterThan(0);
      expect(providers[0]).toHaveProperty('name');
      expect(providers[0]).toHaveProperty('available');
    });

    it('should filter available providers correctly', () => {
      const providers = aiService.getAvailableProviders();
      
      providers.forEach(provider => {
        expect(provider.available).toBe(true);
      });
    });
  });

  describe('rate limiting', () => {
    it('should respect rate limits', async () => {
      const mockMessages = [
        {
          id: '1',
          content: 'Hello',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      // Make multiple rapid requests
      const promises = Array(15).fill(null).map(() => 
        aiService.generateResponse(mockMessages).catch(() => null)
      );

      const results = await Promise.all(promises);
      
      // Some requests should be rate limited
      const successfulRequests = results.filter(result => result !== null);
      expect(successfulRequests.length).toBeLessThanOrEqual(10);
    });
  });

  describe('context building', () => {
    it('should build portfolio context correctly', async () => {
      const mockMessages = [
        {
          id: '1',
          content: 'Tell me about projects',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const
        }
      ];

      const portfolioFocus = ['projects'];

      // Mock the generateContent to capture the prompt
      let capturedPrompt = '';
      const mockGenerateContent = vi.fn().mockImplementation((prompt) => {
        capturedPrompt = prompt;
        return Promise.resolve({
          response: {
            text: () => Promise.resolve('Mock response')
          }
        });
      });
      
      (window as any).generativeai.Defaults = vi.fn().mockImplementation(() => ({
        getGenerativeModel: vi.fn().mockReturnValue({
          generateContent: mockGenerateContent
        })
      }));

      await aiService.generateResponse(mockMessages, portfolioFocus);

      expect(capturedPrompt).toContain('portfolio');
      expect(capturedPrompt).toContain('projects');
    });
  });
});
