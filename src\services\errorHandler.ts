/**
 * Enhanced Error Handling System for Chatbot
 * 
 * Provides comprehensive error handling, user feedback,
 * and graceful degradation for different failure scenarios.
 */

import { ChatMessage } from '@/types';
import { AIServiceError } from './aiService';

export interface ErrorContext {
  userMessage: string;
  timestamp: Date;
  sessionId: string;
  retryCount?: number;
  lastError?: Error;
}

export interface ErrorResponse {
  message: string;
  type: 'error' | 'warning' | 'info';
  retryable: boolean;
  suggestedActions?: string[];
  metadata?: any;
}

export class ChatbotErrorHandler {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAYS = [1000, 2000, 4000]; // Progressive backoff

  /**
   * Handle errors with appropriate user feedback
   */
  static handleError(error: Error, context: ErrorContext): ErrorResponse {
    console.error('🚨 ChatbotErrorHandler: Processing error:', error);

    // Handle AI Service specific errors
    if (error instanceof AIServiceError) {
      return this.handleAIServiceError(error, context);
    }

    // Handle network errors
    if (this.isNetworkError(error)) {
      return this.handleNetworkError(error, context);
    }

    // Handle quota/rate limit errors
    if (this.isQuotaError(error)) {
      return this.handleQuotaError(error, context);
    }

    // Handle generic errors
    return this.handleGenericError(error, context);
  }

  /**
   * Handle AI Service specific errors
   */
  private static handleAIServiceError(error: AIServiceError, context: ErrorContext): ErrorResponse {
    switch (error.code) {
      case 'NO_API_KEY':
        return {
          message: `🔧 **AI Mode Unavailable**

I'm currently running in **fallback mode** because the Gemini API key isn't configured. 

💡 **What this means:**
• I can still help with portfolio navigation
• Responses use smart keyword matching
• All portfolio information is available

🛠️ **To enable AI mode:**
• Set the GEMINI_API_KEY environment variable
• Restart the development server
• Refresh this page

*I'm still here to help! Try asking about projects, skills, or experience.*`,
          type: 'info',
          retryable: false,
          suggestedActions: [
            'Ask about specific projects',
            'Explore technical skills',
            'View work experience',
            'Check education background'
          ],
          metadata: { errorCode: error.code, fallbackMode: true }
        };

      case 'RATE_LIMIT_EXCEEDED':
        return {
          message: `⏱️ **Rate Limit Reached**

I'm receiving many requests right now. Let me help you in fallback mode while we wait.

💡 **What you can do:**
• Continue asking questions (I'll use smart matching)
• Try again in a few minutes for AI responses
• Use the quick action buttons below

*I'm still fully functional for portfolio exploration!*`,
          type: 'warning',
          retryable: true,
          suggestedActions: [
            'Use quick action buttons',
            'Ask about specific topics',
            'Try again in 2-3 minutes'
          ],
          metadata: { errorCode: error.code, retryAfter: 120 }
        };

      case 'SDK_NOT_LOADED':
        return {
          message: `🔄 **AI Features Loading**

The AI system is still initializing. I'll use fallback mode for now.

💡 **This usually resolves automatically:**
• Refresh the page if it persists
• Check your internet connection
• Try asking your question again

*I can still help with portfolio navigation!*`,
          type: 'warning',
          retryable: true,
          suggestedActions: [
            'Refresh the page',
            'Try your question again',
            'Use fallback responses'
          ],
          metadata: { errorCode: error.code }
        };

      case 'GEMINI_API_ERROR':
        return {
          message: `🤖 **AI Service Temporarily Unavailable**

There's a temporary issue with the AI service. I'll use fallback mode to help you.

💡 **Don't worry:**
• All portfolio information is still accessible
• I can navigate you to relevant sections
• Try again in a few moments for AI responses

*Let me help you explore the portfolio!*`,
          type: 'warning',
          retryable: true,
          suggestedActions: [
            'Continue with fallback responses',
            'Try again in a few minutes',
            'Use navigation features'
          ],
          metadata: { errorCode: error.code }
        };

      default:
        return {
          message: `⚠️ **AI Service Issue**

Encountered an unexpected AI service error. Switching to fallback mode.

💡 **I can still help with:**
• Portfolio navigation
• Project information
• Skills and experience details

*What would you like to explore?*`,
          type: 'warning',
          retryable: error.retryable,
          suggestedActions: [
            'Ask about projects',
            'Explore skills',
            'View experience'
          ],
          metadata: { errorCode: error.code }
        };
    }
  }

  /**
   * Handle network-related errors
   */
  private static handleNetworkError(error: Error, context: ErrorContext): ErrorResponse {
    return {
      message: `🌐 **Connection Issue**

There seems to be a network connectivity issue. I'll work in offline mode.

💡 **What's available:**
• All portfolio content is cached locally
• Navigation and search still work
• Try refreshing if the issue persists

*I'm still here to help with portfolio exploration!*`,
      type: 'warning',
      retryable: true,
      suggestedActions: [
        'Check your internet connection',
        'Refresh the page',
        'Continue with offline features'
      ],
      metadata: { networkError: true }
    };
  }

  /**
   * Handle quota/rate limit errors
   */
  private static handleQuotaError(error: Error, context: ErrorContext): ErrorResponse {
    return {
      message: `📊 **Usage Limit Reached**

The AI service has reached its usage limit for now. I'll use fallback mode.

💡 **What this means:**
• Responses will use smart keyword matching
• All portfolio information remains available
• AI features will return later

*I'm still fully functional for portfolio exploration!*`,
      type: 'info',
      retryable: true,
      suggestedActions: [
        'Continue with fallback mode',
        'Try again later',
        'Use quick action buttons'
      ],
      metadata: { quotaExceeded: true }
    };
  }

  /**
   * Handle generic errors
   */
  private static handleGenericError(error: Error, context: ErrorContext): ErrorResponse {
    return {
      message: `🔧 **Technical Issue**

Something unexpected happened, but I'm still here to help!

💡 **I can assist with:**
• Portfolio navigation
• Project details
• Skills and experience information

*What would you like to explore?*`,
      type: 'error',
      retryable: true,
      suggestedActions: [
        'Try your question again',
        'Use different keywords',
        'Refresh the page if needed'
      ],
      metadata: { genericError: true, errorMessage: error.message }
    };
  }

  /**
   * Check if error is network-related
   */
  private static isNetworkError(error: Error): boolean {
    const networkErrorMessages = [
      'network error',
      'fetch failed',
      'connection refused',
      'timeout',
      'offline'
    ];
    
    return networkErrorMessages.some(msg => 
      error.message.toLowerCase().includes(msg)
    );
  }

  /**
   * Check if error is quota-related
   */
  private static isQuotaError(error: Error): boolean {
    const quotaErrorMessages = [
      'quota exceeded',
      'usage limit',
      'billing',
      'payment required'
    ];
    
    return quotaErrorMessages.some(msg => 
      error.message.toLowerCase().includes(msg)
    );
  }

  /**
   * Determine if error should trigger retry
   */
  static shouldRetry(error: Error, retryCount: number = 0): boolean {
    if (retryCount >= this.MAX_RETRIES) {
      return false;
    }

    // Don't retry for certain error types
    if (error instanceof AIServiceError) {
      switch (error.code) {
        case 'NO_API_KEY':
        case 'INVALID_API_KEY':
          return false;
        default:
          return error.retryable;
      }
    }

    // Retry network errors
    if (this.isNetworkError(error)) {
      return true;
    }

    // Don't retry quota errors
    if (this.isQuotaError(error)) {
      return false;
    }

    return true;
  }

  /**
   * Get retry delay for progressive backoff
   */
  static getRetryDelay(retryCount: number): number {
    return this.RETRY_DELAYS[Math.min(retryCount, this.RETRY_DELAYS.length - 1)];
  }

  /**
   * Create user-friendly error message for display
   */
  static createUserMessage(errorResponse: ErrorResponse, context: ErrorContext): ChatMessage {
    return {
      id: Date.now().toString(),
      content: errorResponse.message,
      sender: 'assistant',
      timestamp: new Date(),
      status: 'sent',
      metadata: {
        isError: true,
        errorType: errorResponse.type,
        retryable: errorResponse.retryable,
        suggestedActions: errorResponse.suggestedActions,
        ...errorResponse.metadata
      }
    };
  }
}

/**
 * Enhanced fallback response generator with better error context
 */
export class EnhancedFallbackGenerator {
  /**
   * Generate contextual fallback response
   */
  static generateResponse(query: string, errorContext?: ErrorContext): string {
    const lowerQuery = query.toLowerCase();

    // Provide helpful fallback based on query intent
    if (this.isProjectQuery(lowerQuery)) {
      return this.getProjectFallback();
    }

    if (this.isSkillsQuery(lowerQuery)) {
      return this.getSkillsFallback();
    }

    if (this.isExperienceQuery(lowerQuery)) {
      return this.getExperienceFallback();
    }

    if (this.isContactQuery(lowerQuery)) {
      return this.getContactFallback();
    }

    // Default helpful response
    return this.getDefaultFallback();
  }

  private static isProjectQuery(query: string): boolean {
    return ['project', 'work', 'built', 'created', 'developed'].some(keyword => 
      query.includes(keyword)
    );
  }

  private static isSkillsQuery(query: string): boolean {
    return ['skill', 'technology', 'language', 'framework', 'tool'].some(keyword => 
      query.includes(keyword)
    );
  }

  private static isExperienceQuery(query: string): boolean {
    return ['experience', 'job', 'work', 'career', 'internship'].some(keyword => 
      query.includes(keyword)
    );
  }

  private static isContactQuery(query: string): boolean {
    return ['contact', 'email', 'reach', 'hire', 'connect'].some(keyword => 
      query.includes(keyword)
    );
  }

  private static getProjectFallback(): string {
    return `🚀 **Projects Overview**

I can help you explore Vansh's projects! Here are some highlights:

• **AI/ML Projects** - Machine learning and AI implementations
• **Web Scraping** - Data extraction and automation tools  
• **Full-Stack Applications** - Complete web solutions
• **Data Analysis** - Analytics and visualization projects

*Use the quick action buttons below or ask about specific technologies!*`;
  }

  private static getSkillsFallback(): string {
    return `💻 **Technical Skills**

Vansh has expertise in multiple areas:

• **Programming:** Python, JavaScript, TypeScript, Java
• **Web Development:** React, Node.js, HTML/CSS, Tailwind
• **Data Science:** pandas, scikit-learn, TensorFlow
• **Tools:** Git, Docker, AWS, Google Cloud

*Ask about any specific technology or use the quick actions!*`;
  }

  private static getExperienceFallback(): string {
    return `💼 **Work Experience**

Vansh has professional experience including:

• **AIML Development Engineer Intern** at EaseMyMed
• **Backend Development** with Django and APIs
• **AI Integration** with OpenAI and Gemini models
• **Cloud Deployment** on AWS and Google Cloud

*Use the experience quick action for detailed information!*`;
  }

  private static getContactFallback(): string {
    return `📧 **Get In Touch**

You can connect with Vansh through:

• **Email:** Professional contact available
• **LinkedIn:** Professional networking
• **GitHub:** Code repositories and projects
• **Portfolio:** This website with full details

*Check the contact section for all details!*`;
  }

  private static getDefaultFallback(): string {
    return `👋 **I'm here to help!**

I can provide information about:

• **Projects** - Technical work and achievements
• **Skills** - Programming languages and technologies  
• **Experience** - Professional background
• **Education** - Academic qualifications

*Try the quick action buttons or ask about anything specific!*`;
  }
}
