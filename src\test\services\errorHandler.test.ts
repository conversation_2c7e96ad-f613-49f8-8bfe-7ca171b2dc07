import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ChatbotError<PERSON><PERSON><PERSON>, EnhancedFallbackGenerator } from '../../services/errorHandler';
import { AIServiceError } from '../../services/aiService';

describe('ChatbotErrorHandler', () => {
  const mockErrorContext = {
    userMessage: 'Test message',
    timestamp: new Date(),
    sessionId: 'test-session-123'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('handleError', () => {
    it('should handle NO_API_KEY error correctly', () => {
      const error = new AIServiceError('No API key', 'NO_API_KEY');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('info');
      expect(response.retryable).toBe(false);
      expect(response.message).toContain('AI Mode Unavailable');
      expect(response.message).toContain('fallback mode');
      expect(response.suggestedActions).toContain('Ask about specific projects');
    });

    it('should handle RATE_LIMIT_EXCEEDED error correctly', () => {
      const error = new AIServiceError('Rate limit exceeded', 'RATE_LIMIT_EXCEEDED');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('warning');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('Rate Limit Reached');
      expect(response.metadata?.retryAfter).toBe(120);
    });

    it('should handle SDK_NOT_LOADED error correctly', () => {
      const error = new AIServiceError('SDK not loaded', 'SDK_NOT_LOADED');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('warning');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('AI Features Loading');
      expect(response.suggestedActions).toContain('Refresh the page');
    });

    it('should handle GEMINI_API_ERROR correctly', () => {
      const error = new AIServiceError('API error', 'GEMINI_API_ERROR');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('warning');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('AI Service Temporarily Unavailable');
    });

    it('should handle network errors correctly', () => {
      const error = new Error('Network error: fetch failed');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('warning');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('Connection Issue');
      expect(response.metadata?.networkError).toBe(true);
    });

    it('should handle quota errors correctly', () => {
      const error = new Error('Quota exceeded for this project');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('info');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('Usage Limit Reached');
      expect(response.metadata?.quotaExceeded).toBe(true);
    });

    it('should handle generic errors correctly', () => {
      const error = new Error('Unknown error occurred');
      
      const response = ChatbotErrorHandler.handleError(error, mockErrorContext);
      
      expect(response.type).toBe('error');
      expect(response.retryable).toBe(true);
      expect(response.message).toContain('Technical Issue');
      expect(response.metadata?.genericError).toBe(true);
    });
  });

  describe('shouldRetry', () => {
    it('should not retry after max attempts', () => {
      const error = new Error('Retryable error');
      
      expect(ChatbotErrorHandler.shouldRetry(error, 3)).toBe(false);
    });

    it('should not retry NO_API_KEY errors', () => {
      const error = new AIServiceError('No API key', 'NO_API_KEY');
      
      expect(ChatbotErrorHandler.shouldRetry(error, 0)).toBe(false);
    });

    it('should retry network errors', () => {
      const error = new Error('Network timeout');
      
      expect(ChatbotErrorHandler.shouldRetry(error, 0)).toBe(true);
    });

    it('should not retry quota errors', () => {
      const error = new Error('Quota exceeded');
      
      expect(ChatbotErrorHandler.shouldRetry(error, 0)).toBe(false);
    });

    it('should respect AIServiceError retryable flag', () => {
      const retryableError = new AIServiceError('Retryable', 'TEST_ERROR', 'test', true);
      const nonRetryableError = new AIServiceError('Non-retryable', 'TEST_ERROR', 'test', false);
      
      expect(ChatbotErrorHandler.shouldRetry(retryableError, 0)).toBe(true);
      expect(ChatbotErrorHandler.shouldRetry(nonRetryableError, 0)).toBe(false);
    });
  });

  describe('getRetryDelay', () => {
    it('should return progressive backoff delays', () => {
      expect(ChatbotErrorHandler.getRetryDelay(0)).toBe(1000);
      expect(ChatbotErrorHandler.getRetryDelay(1)).toBe(2000);
      expect(ChatbotErrorHandler.getRetryDelay(2)).toBe(4000);
      expect(ChatbotErrorHandler.getRetryDelay(10)).toBe(4000); // Max delay
    });
  });

  describe('createUserMessage', () => {
    it('should create properly formatted user message', () => {
      const errorResponse = {
        message: 'Test error message',
        type: 'error' as const,
        retryable: true,
        suggestedActions: ['Try again'],
        metadata: { test: true }
      };
      
      const message = ChatbotErrorHandler.createUserMessage(errorResponse, mockErrorContext);
      
      expect(message.content).toBe('Test error message');
      expect(message.sender).toBe('assistant');
      expect(message.status).toBe('sent');
      expect(message.metadata?.isError).toBe(true);
      expect(message.metadata?.errorType).toBe('error');
      expect(message.metadata?.retryable).toBe(true);
      expect(message.metadata?.suggestedActions).toEqual(['Try again']);
    });
  });
});

describe('EnhancedFallbackGenerator', () => {
  const mockErrorContext = {
    userMessage: 'Test message',
    timestamp: new Date(),
    sessionId: 'test-session-123'
  };

  describe('generateResponse', () => {
    it('should generate project fallback for project queries', () => {
      const response = EnhancedFallbackGenerator.generateResponse(
        'Tell me about your projects',
        mockErrorContext
      );
      
      expect(response).toContain('Projects Overview');
      expect(response).toContain('AI/ML Projects');
      expect(response).toContain('Web Scraping');
    });

    it('should generate skills fallback for skills queries', () => {
      const response = EnhancedFallbackGenerator.generateResponse(
        'What programming languages do you know?',
        mockErrorContext
      );
      
      expect(response).toContain('Technical Skills');
      expect(response).toContain('Programming:');
      expect(response).toContain('Python, JavaScript');
    });

    it('should generate experience fallback for experience queries', () => {
      const response = EnhancedFallbackGenerator.generateResponse(
        'Tell me about your work experience',
        mockErrorContext
      );
      
      expect(response).toContain('Work Experience');
      expect(response).toContain('AIML Development Engineer');
      expect(response).toContain('EaseMyMed');
    });

    it('should generate contact fallback for contact queries', () => {
      const response = EnhancedFallbackGenerator.generateResponse(
        'How can I contact you?',
        mockErrorContext
      );
      
      expect(response).toContain('Get In Touch');
      expect(response).toContain('Email:');
      expect(response).toContain('LinkedIn:');
    });

    it('should generate default fallback for unrecognized queries', () => {
      const response = EnhancedFallbackGenerator.generateResponse(
        'Random question about something',
        mockErrorContext
      );
      
      expect(response).toContain("I'm here to help!");
      expect(response).toContain('Projects');
      expect(response).toContain('Skills');
      expect(response).toContain('Experience');
    });

    it('should handle empty queries gracefully', () => {
      const response = EnhancedFallbackGenerator.generateResponse('', mockErrorContext);
      
      expect(response).toContain("I'm here to help!");
      expect(typeof response).toBe('string');
      expect(response.length).toBeGreaterThan(0);
    });
  });

  describe('query classification', () => {
    it('should correctly identify project queries', () => {
      const projectQueries = [
        'What projects have you built?',
        'Show me your work',
        'What have you developed?',
        'Tell me about something you created'
      ];
      
      projectQueries.forEach(query => {
        const response = EnhancedFallbackGenerator.generateResponse(query);
        expect(response).toContain('Projects Overview');
      });
    });

    it('should correctly identify skills queries', () => {
      const skillsQueries = [
        'What skills do you have?',
        'What technologies do you know?',
        'What programming languages?',
        'What frameworks do you use?'
      ];
      
      skillsQueries.forEach(query => {
        const response = EnhancedFallbackGenerator.generateResponse(query);
        expect(response).toContain('Technical Skills');
      });
    });

    it('should correctly identify experience queries', () => {
      const experienceQueries = [
        'Tell me about your experience',
        'Where have you worked?',
        'What jobs have you had?',
        'Your career background'
      ];
      
      experienceQueries.forEach(query => {
        const response = EnhancedFallbackGenerator.generateResponse(query);
        expect(response).toContain('Work Experience');
      });
    });
  });
});
