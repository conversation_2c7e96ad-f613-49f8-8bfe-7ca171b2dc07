import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';

// Mock data for testing
export const mockPortfolioData = {
  personal: {
    name: 'Test User',
    title: 'Test Developer',
    email: '<EMAIL>'
  },
  skills: {
    programming: ['JavaScript', 'TypeScript', 'Python'],
    frameworks: ['React', 'Node.js', 'Django'],
    tools: ['Git', 'Docker', 'AWS']
  },
  projects: [
    {
      id: 'test-project-1',
      title: 'Test Project 1',
      description: 'A test project for unit testing',
      technologies: ['React', 'TypeScript'],
      github: 'https://github.com/test/project1'
    },
    {
      id: 'test-project-2',
      title: 'Test Project 2',
      description: 'Another test project',
      technologies: ['Python', 'Django'],
      github: 'https://github.com/test/project2'
    }
  ],
  experience: [
    {
      id: 'test-exp-1',
      company: 'Test Company',
      position: 'Test Developer',
      duration: '2023 - Present',
      description: 'Test development work'
    }
  ]
};

export const mockChatMessages = [
  {
    id: '1',
    content: 'Hello!',
    sender: 'user' as const,
    timestamp: new Date('2024-01-01T10:00:00Z'),
    status: 'sent' as const
  },
  {
    id: '2',
    content: 'Hi! How can I help you?',
    sender: 'assistant' as const,
    timestamp: new Date('2024-01-01T10:00:01Z'),
    status: 'sent' as const
  }
];

export const mockConversationContext = {
  messages: mockChatMessages,
  sessionId: 'test-session-123',
  startTime: new Date('2024-01-01T10:00:00Z'),
  lastActivity: new Date('2024-01-01T10:00:01Z'),
  portfolioFocus: ['projects', 'skills']
};

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <div data-testid="test-wrapper">
      {children}
    </div>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Test helpers
export const createMockApiService = () => ({
  initialize: vi.fn().mockResolvedValue(undefined),
  generateResponse: vi.fn().mockResolvedValue({
    content: 'Mock AI response',
    metadata: { model: 'test-model', processingTime: 100 }
  }),
  isAvailable: vi.fn().mockReturnValue(true),
  getAvailableProviders: vi.fn().mockReturnValue([
    { name: 'gemini', model: 'gemini-2.0-flash', available: true, priority: 1 }
  ])
});

export const createMockConversationManager = () => ({
  getMessages: vi.fn().mockReturnValue(mockChatMessages),
  getContext: vi.fn().mockReturnValue(mockConversationContext),
  sendMessage: vi.fn().mockResolvedValue(undefined),
  clearConversation: vi.fn(),
  isAIAvailable: vi.fn().mockReturnValue(true),
  onMessagesChange: vi.fn().mockReturnValue(() => {}),
  onStatusChange: vi.fn().mockReturnValue(() => {})
});

export const createMockErrorHandler = () => ({
  handleError: vi.fn().mockReturnValue({
    message: 'Mock error message',
    type: 'error',
    retryable: false,
    suggestedActions: ['Try again'],
    metadata: {}
  }),
  shouldRetry: vi.fn().mockReturnValue(false),
  getRetryDelay: vi.fn().mockReturnValue(1000),
  createUserMessage: vi.fn().mockReturnValue({
    id: 'error-msg-1',
    content: 'Error occurred',
    sender: 'assistant',
    timestamp: new Date(),
    status: 'sent',
    metadata: { isError: true }
  })
});

// Mock API responses
export const mockApiResponses = {
  success: {
    content: 'This is a successful AI response with helpful information.',
    metadata: {
      model: 'gemini-2.0-flash',
      processingTime: 150,
      provider: 'gemini',
      tokens: 45
    }
  },
  error: new Error('API request failed'),
  rateLimit: new Error('Rate limit exceeded'),
  noApiKey: new Error('No API key provided')
};

// Environment helpers
export const mockEnvironment = (overrides: Record<string, any> = {}) => {
  const originalEnv = { ...import.meta.env };
  
  Object.assign(import.meta.env, {
    MODE: 'test',
    DEV: false,
    PROD: false,
    GEMINI_API_KEY: 'test-api-key',
    ...overrides
  });

  return () => {
    Object.assign(import.meta.env, originalEnv);
  };
};

// Wait helpers
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const waitForCondition = async (
  condition: () => boolean,
  timeout = 5000,
  interval = 100
): Promise<void> => {
  const startTime = Date.now();
  
  while (!condition() && Date.now() - startTime < timeout) {
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  if (!condition()) {
    throw new Error(`Condition not met within ${timeout}ms`);
  }
};

// DOM helpers
export const createMockElement = (tag: string = 'div', attributes: Record<string, string> = {}) => {
  const element = document.createElement(tag);
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
  return element;
};

export const mockScrollIntoView = () => {
  Element.prototype.scrollIntoView = vi.fn();
};

// Console helpers
export const mockConsole = () => {
  const originalConsole = { ...console };
  
  console.log = vi.fn();
  console.warn = vi.fn();
  console.error = vi.fn();
  console.info = vi.fn();
  
  return () => {
    Object.assign(console, originalConsole);
  };
};

// Local storage helpers
export const mockLocalStorage = (initialData: Record<string, string> = {}) => {
  const storage = { ...initialData };
  
  const mockStorage = {
    getItem: vi.fn((key: string) => storage[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach(key => delete storage[key]);
    }),
    length: 0,
    key: vi.fn()
  };

  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
    writable: true
  });

  return mockStorage;
};
