/* Non-critical CSS that can be loaded asynchronously */

/* Animations that aren't needed for initial render */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Styles for components that appear below the fold */
.testimonial-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Styles for the contact form which is typically below the fold */
.contact-form-input:focus {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  border-color: #4f46e5;
}

.contact-form-textarea {
  min-height: 150px;
  resize: vertical;
}

/* Styles for the footer which is always below the fold */
.footer-link:hover {
  text-decoration: underline;
}

/* Styles for the project details which are typically viewed after clicking a project */
.project-details-image {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.project-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 6px;
  margin-bottom: 6px;
}

/* Styles for the skills section which may be below the fold */
.skill-bar {
  height: 8px;
  border-radius: 4px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  border-radius: 4px;
  background-color: #4f46e5;
  transition: width 1s ease-in-out;
}

/* Print styles - only needed when printing */
@media print {
  header, footer, nav, .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    background: #fff;
  }
  
  a {
    text-decoration: none;
    color: #000;
  }
  
  .page-break {
    page-break-before: always;
  }
}
