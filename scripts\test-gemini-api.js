#!/usr/bin/env node

/**
 * Gemini API Key Test Script
 * 
 * This script tests the Gemini API key configuration and functionality.
 * It verifies environment variable detection, API key format validation,
 * and basic API connectivity.
 * 
 * Usage:
 *   node scripts/test-gemini-api.js
 * 
 * Environment Variables:
 *   GEMINI_API_KEY - Your Google Gemini API key
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

/**
 * Log a test result
 */
function logTest(name, status, message, details = null) {
  const timestamp = new Date().toISOString();
  const statusColor = status === 'PASS' ? colors.green : 
                     status === 'FAIL' ? colors.red : 
                     colors.yellow;
  
  console.log(`${statusColor}[${status}]${colors.reset} ${colors.bright}${name}${colors.reset}`);
  if (message) {
    console.log(`  ${message}`);
  }
  if (details) {
    console.log(`  ${colors.cyan}Details:${colors.reset} ${JSON.stringify(details, null, 2)}`);
  }
  console.log('');

  testResults.tests.push({
    name,
    status,
    message,
    details,
    timestamp
  });

  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') testResults.failed++;
  else testResults.warnings++;
}

/**
 * Test environment variable detection
 */
function testEnvironmentVariables() {
  console.log(`${colors.blue}=== Environment Variable Tests ===${colors.reset}\n`);

  // Test 1: Check if process.env is available
  const hasProcessEnv = typeof process !== 'undefined' && !!process.env;
  logTest(
    'Process Environment Available',
    hasProcessEnv ? 'PASS' : 'FAIL',
    hasProcessEnv ? 'process.env is accessible' : 'process.env is not available',
    { hasProcessEnv, nodeVersion: process.version }
  );

  // Test 2: Check for GEMINI_API_KEY
  const geminiApiKey = process.env.GEMINI_API_KEY;
  logTest(
    'GEMINI_API_KEY Environment Variable',
    geminiApiKey ? 'PASS' : 'FAIL',
    geminiApiKey ? 'GEMINI_API_KEY is set' : 'GEMINI_API_KEY is not set',
    {
      hasKey: !!geminiApiKey,
      keyLength: geminiApiKey?.length || 0,
      keyPreview: geminiApiKey ? `${geminiApiKey.substring(0, 10)}...` : null
    }
  );

  // Test 3: Check for alternative environment variables
  const altKeys = ['GOOGLE_API_KEY', 'VITE_GEMINI_API_KEY', 'REACT_APP_GEMINI_API_KEY'];
  const foundAltKeys = altKeys.filter(key => process.env[key]);
  
  if (foundAltKeys.length > 0) {
    logTest(
      'Alternative API Keys Found',
      'WARN',
      `Found alternative environment variables: ${foundAltKeys.join(', ')}`,
      { alternativeKeys: foundAltKeys }
    );
  }

  return geminiApiKey;
}

/**
 * Test API key format validation
 */
function testApiKeyFormat(apiKey) {
  console.log(`${colors.blue}=== API Key Format Tests ===${colors.reset}\n`);

  if (!apiKey) {
    logTest(
      'API Key Format Validation',
      'FAIL',
      'No API key provided for format validation'
    );
    return false;
  }

  // Test 1: Basic format check
  const isValidLength = apiKey.length >= 30; // Gemini API keys are typically longer
  logTest(
    'API Key Length',
    isValidLength ? 'PASS' : 'FAIL',
    `API key length: ${apiKey.length} characters`,
    { length: apiKey.length, minExpected: 30 }
  );

  // Test 2: Character composition
  const hasValidChars = /^[A-Za-z0-9_-]+$/.test(apiKey);
  logTest(
    'API Key Character Validation',
    hasValidChars ? 'PASS' : 'WARN',
    hasValidChars ? 'API key contains valid characters' : 'API key contains unexpected characters',
    { pattern: '^[A-Za-z0-9_-]+$', matches: hasValidChars }
  );

  // Test 3: Prefix check (Gemini keys often start with specific patterns)
  const hasValidPrefix = apiKey.startsWith('AIza') || apiKey.startsWith('AI');
  logTest(
    'API Key Prefix',
    hasValidPrefix ? 'PASS' : 'WARN',
    hasValidPrefix ? 'API key has expected prefix' : 'API key prefix may be non-standard',
    { prefix: apiKey.substring(0, 4), expectedPrefixes: ['AIza', 'AI'] }
  );

  return isValidLength && hasValidChars;
}

/**
 * Test API connectivity (mock test for browser environment)
 */
function testApiConnectivity(apiKey) {
  console.log(`${colors.blue}=== API Connectivity Tests ===${colors.reset}\n`);

  if (!apiKey) {
    logTest(
      'API Connectivity Test',
      'FAIL',
      'Cannot test connectivity without API key'
    );
    return false;
  }

  // Test 1: Mock API endpoint validation
  const apiEndpoint = 'https://generativelanguage.googleapis.com/v1beta/models';
  logTest(
    'API Endpoint Configuration',
    'PASS',
    `Using endpoint: ${apiEndpoint}`,
    { endpoint: apiEndpoint, method: 'GET' }
  );

  // Test 2: Simulate browser environment check
  const isBrowserEnv = typeof window !== 'undefined';
  logTest(
    'Browser Environment Check',
    !isBrowserEnv ? 'PASS' : 'WARN',
    isBrowserEnv ? 'Running in browser environment' : 'Running in Node.js environment',
    { environment: isBrowserEnv ? 'browser' : 'node' }
  );

  // Test 3: Mock SDK availability check
  logTest(
    'Google Generative AI SDK',
    'WARN',
    'SDK availability can only be tested in browser environment',
    { note: 'This test requires browser environment with loaded SDK' }
  );

  return true;
}

/**
 * Test configuration file integration
 */
function testConfigurationIntegration() {
  console.log(`${colors.blue}=== Configuration Integration Tests ===${colors.reset}\n`);

  try {
    // Test 1: Check if aiService.ts exists and is readable
    const aiServicePath = join(__dirname, '..', 'src', 'services', 'aiService.ts');
    const aiServiceContent = readFileSync(aiServicePath, 'utf8');
    
    logTest(
      'AI Service File',
      'PASS',
      'aiService.ts is accessible',
      { path: aiServicePath, size: aiServiceContent.length }
    );

    // Test 2: Check for GEMINI_API_KEY references
    const hasGeminiKeyRef = aiServiceContent.includes('GEMINI_API_KEY');
    logTest(
      'GEMINI_API_KEY References',
      hasGeminiKeyRef ? 'PASS' : 'FAIL',
      hasGeminiKeyRef ? 'Found GEMINI_API_KEY references in aiService.ts' : 'No GEMINI_API_KEY references found',
      { hasReference: hasGeminiKeyRef }
    );

    // Test 3: Check for proper error handling
    const hasErrorHandling = aiServiceContent.includes('AIServiceError');
    logTest(
      'Error Handling Implementation',
      hasErrorHandling ? 'PASS' : 'WARN',
      hasErrorHandling ? 'Error handling classes found' : 'Limited error handling detected',
      { hasErrorHandling }
    );

  } catch (error) {
    logTest(
      'Configuration File Access',
      'FAIL',
      `Failed to read configuration files: ${error.message}`,
      { error: error.message }
    );
  }
}

/**
 * Generate test report
 */
function generateReport() {
  console.log(`${colors.magenta}=== Test Report ===${colors.reset}\n`);
  
  const total = testResults.passed + testResults.failed + testResults.warnings;
  const passRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
  
  console.log(`${colors.bright}Summary:${colors.reset}`);
  console.log(`  ${colors.green}✓ Passed: ${testResults.passed}${colors.reset}`);
  console.log(`  ${colors.red}✗ Failed: ${testResults.failed}${colors.reset}`);
  console.log(`  ${colors.yellow}⚠ Warnings: ${testResults.warnings}${colors.reset}`);
  console.log(`  ${colors.cyan}Pass Rate: ${passRate}%${colors.reset}`);
  console.log('');

  // Recommendations
  console.log(`${colors.bright}Recommendations:${colors.reset}`);
  
  if (testResults.failed > 0) {
    console.log(`  ${colors.red}• Fix failed tests before proceeding${colors.reset}`);
  }
  
  if (!process.env.GEMINI_API_KEY) {
    console.log(`  ${colors.yellow}• Set GEMINI_API_KEY environment variable${colors.reset}`);
    console.log(`    ${colors.cyan}export GEMINI_API_KEY=your_api_key_here${colors.reset}`);
  }
  
  console.log(`  ${colors.green}• Restart development server after setting environment variables${colors.reset}`);
  console.log(`  ${colors.green}• Check browser console for runtime API key detection${colors.reset}`);
  console.log('');

  return testResults.failed === 0;
}

/**
 * Main test execution
 */
async function runTests() {
  console.log(`${colors.bright}${colors.cyan}🤖 Gemini API Key Test Suite${colors.reset}\n`);
  console.log(`${colors.bright}Testing Gemini API integration for portfolio chatbot${colors.reset}\n`);

  try {
    // Run all test suites
    const apiKey = testEnvironmentVariables();
    testApiKeyFormat(apiKey);
    testApiConnectivity(apiKey);
    testConfigurationIntegration();
    
    // Generate final report
    const success = generateReport();
    
    // Exit with appropriate code
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    console.error(`${colors.red}Fatal error during testing:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run tests immediately
runTests();

export { runTests, testResults };
