import React from 'react';
import { ExternalLink, Code, Quote, CheckCircle, AlertCircle, Info } from 'lucide-react';

/**
 * Enhanced message formatter with improved markdown support
 * and professional styling for chatbot responses
 */

export interface FormattingOptions {
  enableCodeBlocks?: boolean;
  enableLinks?: boolean;
  enableEmojis?: boolean;
  enableCallouts?: boolean;
  maxLineLength?: number;
}

const defaultOptions: FormattingOptions = {
  enableCodeBlocks: true,
  enableLinks: true,
  enableEmojis: true,
  enableCallouts: true,
  maxLineLength: 80
};

/**
 * Format bot message with enhanced markdown support
 */
export function formatBotMessage(content: string, options: FormattingOptions = {}): React.ReactNode {
  const opts = { ...defaultOptions, ...options };
  const lines = content.split('\n');
  const elements: React.ReactNode[] = [];
  let inCodeBlock = false;
  let codeBlockContent: string[] = [];
  let codeBlockLanguage = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Handle code blocks
    if (opts.enableCodeBlocks && trimmedLine.startsWith('```')) {
      if (!inCodeBlock) {
        // Start code block
        inCodeBlock = true;
        codeBlockLanguage = trimmedLine.substring(3).trim();
        codeBlockContent = [];
      } else {
        // End code block
        inCodeBlock = false;
        elements.push(
          <div key={`code-${i}`} className="my-3">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
              {codeBlockLanguage && (
                <div className="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-xs font-medium text-gray-600 dark:text-gray-400 border-b border-gray-300 dark:border-gray-600">
                  <Code className="w-3 h-3 inline mr-1" />
                  {codeBlockLanguage}
                </div>
              )}
              <pre className="p-3 text-sm overflow-x-auto">
                <code className="text-gray-800 dark:text-gray-200">
                  {codeBlockContent.join('\n')}
                </code>
              </pre>
            </div>
          </div>
        );
        codeBlockContent = [];
        codeBlockLanguage = '';
      }
      continue;
    }

    // If inside code block, collect content
    if (inCodeBlock) {
      codeBlockContent.push(line);
      continue;
    }

    // Handle callouts
    if (opts.enableCallouts && trimmedLine.match(/^(💡|⚠️|❌|✅|ℹ️|🔍)/)) {
      const calloutType = getCalloutType(trimmedLine);
      const calloutContent = trimmedLine.substring(2).trim();
      elements.push(
        <div key={`callout-${i}`} className={`my-3 p-3 rounded-lg border-l-4 ${getCalloutStyles(calloutType)}`}>
          <div className="flex items-start space-x-2">
            {getCalloutIcon(calloutType)}
            <div className="flex-1 text-sm">
              {formatInlineContent(calloutContent, opts)}
            </div>
          </div>
        </div>
      );
      continue;
    }

    // Handle headers
    if (trimmedLine.startsWith('#')) {
      const level = trimmedLine.match(/^#+/)?.[0].length || 1;
      const headerText = trimmedLine.substring(level).trim();
      const headerClass = getHeaderClass(level);
      
      elements.push(
        <div key={`header-${i}`} className={`${headerClass} mb-3 mt-4 first:mt-0`}>
          {formatInlineContent(headerText, opts)}
        </div>
      );
      continue;
    }

    // Handle bullet points
    if (trimmedLine.match(/^[•\-\*]\s/)) {
      const bulletContent = trimmedLine.substring(2).trim();
      elements.push(
        <div key={`bullet-${i}`} className="ml-4 mb-2 flex items-start">
          <span className="text-blue-500 dark:text-blue-400 mr-3 mt-1 text-xs">●</span>
          <div className="flex-1 text-sm">
            {formatInlineContent(bulletContent, opts)}
          </div>
        </div>
      );
      continue;
    }

    // Handle numbered lists
    if (trimmedLine.match(/^\d+\.\s/)) {
      const match = trimmedLine.match(/^(\d+)\.\s(.+)$/);
      if (match) {
        const [, number, listContent] = match;
        elements.push(
          <div key={`numbered-${i}`} className="ml-4 mb-2 flex items-start">
            <span className="text-blue-500 dark:text-blue-400 mr-3 mt-1 text-xs font-medium min-w-[1.5rem]">
              {number}.
            </span>
            <div className="flex-1 text-sm">
              {formatInlineContent(listContent, opts)}
            </div>
          </div>
        );
        continue;
      }
    }

    // Handle blockquotes
    if (trimmedLine.startsWith('>')) {
      const quoteContent = trimmedLine.substring(1).trim();
      elements.push(
        <div key={`quote-${i}`} className="my-3 pl-4 border-l-3 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50 py-2 rounded-r">
          <div className="flex items-start space-x-2">
            <Quote className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm italic text-gray-600 dark:text-gray-400">
              {formatInlineContent(quoteContent, opts)}
            </div>
          </div>
        </div>
      );
      continue;
    }

    // Handle horizontal rules
    if (trimmedLine.match(/^-{3,}$/)) {
      elements.push(
        <hr key={`hr-${i}`} className="my-4 border-gray-300 dark:border-gray-600" />
      );
      continue;
    }

    // Handle empty lines
    if (!trimmedLine) {
      elements.push(<div key={`empty-${i}`} className="mb-2"></div>);
      continue;
    }

    // Handle regular paragraphs
    elements.push(
      <div key={`para-${i}`} className="mb-2 text-sm leading-relaxed">
        {formatInlineContent(line, opts)}
      </div>
    );
  }

  return <div className="space-y-1">{elements}</div>;
}

/**
 * Format inline content (bold, italic, links, inline code)
 */
function formatInlineContent(content: string, options: FormattingOptions): React.ReactNode {
  let result: React.ReactNode = content;

  // Handle inline code
  if (options.enableCodeBlocks) {
    result = formatInlineCode(result as string);
  }

  // Handle bold text
  result = formatBold(result as string);

  // Handle italic text
  result = formatItalic(result as string);

  // Handle links
  if (options.enableLinks) {
    result = formatLinks(result as string);
  }

  return result;
}

/**
 * Format inline code `code`
 */
function formatInlineCode(content: string): React.ReactNode {
  const parts = content.split(/(`[^`]+`)/);
  return parts.map((part, index) => {
    if (part.startsWith('`') && part.endsWith('`')) {
      const code = part.slice(1, -1);
      return (
        <code key={index} className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono text-red-600 dark:text-red-400">
          {code}
        </code>
      );
    }
    return part;
  });
}

/**
 * Format bold text **text**
 */
function formatBold(content: string): React.ReactNode {
  if (typeof content !== 'string') return content;
  
  const parts = content.split(/(\*\*[^*]+\*\*)/);
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      const boldText = part.slice(2, -2);
      return (
        <strong key={index} className="font-semibold text-gray-900 dark:text-gray-100">
          {boldText}
        </strong>
      );
    }
    return part;
  });
}

/**
 * Format italic text *text*
 */
function formatItalic(content: string): React.ReactNode {
  if (typeof content !== 'string') return content;
  
  const parts = content.split(/(\*[^*]+\*)/);
  return parts.map((part, index) => {
    if (part.startsWith('*') && part.endsWith('*') && !part.startsWith('**')) {
      const italicText = part.slice(1, -1);
      return (
        <em key={index} className="italic text-gray-700 dark:text-gray-300">
          {italicText}
        </em>
      );
    }
    return part;
  });
}

/**
 * Format links [text](url)
 */
function formatLinks(content: string): React.ReactNode {
  if (typeof content !== 'string') return content;
  
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  const parts = content.split(linkRegex);
  
  const result: React.ReactNode[] = [];
  for (let i = 0; i < parts.length; i += 3) {
    if (parts[i]) result.push(parts[i]);
    if (parts[i + 1] && parts[i + 2]) {
      result.push(
        <a
          key={i}
          href={parts[i + 2]}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline inline-flex items-center gap-1"
        >
          {parts[i + 1]}
          <ExternalLink className="w-3 h-3" />
        </a>
      );
    }
  }
  
  return result.length > 1 ? result : content;
}

/**
 * Get callout type from emoji
 */
function getCalloutType(line: string): string {
  if (line.startsWith('💡')) return 'info';
  if (line.startsWith('⚠️')) return 'warning';
  if (line.startsWith('❌')) return 'error';
  if (line.startsWith('✅')) return 'success';
  if (line.startsWith('ℹ️')) return 'info';
  if (line.startsWith('🔍')) return 'note';
  return 'info';
}

/**
 * Get callout styles based on type
 */
function getCalloutStyles(type: string): string {
  switch (type) {
    case 'warning':
      return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 dark:border-yellow-600';
    case 'error':
      return 'bg-red-50 dark:bg-red-900/20 border-red-400 dark:border-red-600';
    case 'success':
      return 'bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600';
    case 'note':
      return 'bg-purple-50 dark:bg-purple-900/20 border-purple-400 dark:border-purple-600';
    default:
      return 'bg-blue-50 dark:bg-blue-900/20 border-blue-400 dark:border-blue-600';
  }
}

/**
 * Get callout icon based on type
 */
function getCalloutIcon(type: string): React.ReactNode {
  const iconClass = "w-4 h-4 flex-shrink-0 mt-0.5";
  
  switch (type) {
    case 'warning':
      return <AlertCircle className={`${iconClass} text-yellow-600 dark:text-yellow-400`} />;
    case 'error':
      return <AlertCircle className={`${iconClass} text-red-600 dark:text-red-400`} />;
    case 'success':
      return <CheckCircle className={`${iconClass} text-green-600 dark:text-green-400`} />;
    default:
      return <Info className={`${iconClass} text-blue-600 dark:text-blue-400`} />;
  }
}

/**
 * Get header class based on level
 */
function getHeaderClass(level: number): string {
  switch (level) {
    case 1:
      return 'text-xl font-bold text-gray-900 dark:text-gray-100';
    case 2:
      return 'text-lg font-semibold text-gray-800 dark:text-gray-200';
    case 3:
      return 'text-base font-medium text-gray-800 dark:text-gray-200';
    default:
      return 'text-sm font-medium text-gray-700 dark:text-gray-300';
  }
}
