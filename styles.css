/* Chatbot Styles */
.nexus-chat {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

.nexus-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    animation: bounce 2s infinite;
}

.nexus-icon:hover {
    transform: scale(1.1);
}

.nexus-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100%;
    background: var(--glass);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 182, 216, 0.5);
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.nexus-panel.active {
    right: 0;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    margin-bottom: 15px;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
}

.message {
    margin: 10px 0;
    padding: 12px 18px;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.user-message {
    background: var(--primary);
    margin-left: auto;
    color: #333;
}

.bot-message {
    background: var(--light);
    margin-right: auto;
    color: #333;
}

.ai-indicator {
    font-size: 0.7em;
    margin-top: 5px;
    opacity: 0.7;
    font-style: italic;
}

.maintenance-notice {
    background: rgba(255, 215, 0, 0.2);
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    font-size: 0.9em;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.5);
}

.chat-input {
    display: flex;
    gap: 10px;
}

input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--accent);
    border-radius: 25px;
    outline: none;
    background: rgba(255, 255, 255, 0.9);
    transition: border-color 0.3s;
}

input:focus {
    border-color: var(--primary);
}

button {
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border: none;
    border-radius: 25px;
    color: white;
    cursor: pointer;
    transition: opacity 0.3s;
}

button:hover {
    opacity: 0.9;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@media (max-width: 480px) {
    .nexus-panel {
        width: 100%;
    }
}