import React from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  <PERSON><PERSON>ircle, 
  Wifi, 
  Wif<PERSON><PERSON>ff, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>,
  <PERSON>,
  RefreshCw
} from 'lucide-react';

export type ChatbotStatus = 
  | 'online' 
  | 'ai-mode' 
  | 'fallback-mode' 
  | 'error' 
  | 'loading' 
  | 'retrying'
  | 'offline';

interface ChatbotStatusIndicatorProps {
  status: ChatbotStatus;
  message?: string;
  retryCount?: number;
  className?: string;
}

const ChatbotStatusIndicator: React.FC<ChatbotStatusIndicatorProps> = ({
  status,
  message,
  retryCount = 0,
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'ai-mode':
        return {
          icon: <Bot className="w-3 h-3" />,
          color: 'text-green-500',
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          borderColor: 'border-green-200 dark:border-green-700',
          label: 'AI Mode',
          description: message || 'Full AI capabilities active'
        };
      
      case 'fallback-mode':
        return {
          icon: <Zap className="w-3 h-3" />,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          borderColor: 'border-yellow-200 dark:border-yellow-700',
          label: 'Fallback Mode',
          description: message || 'Smart keyword matching active'
        };
      
      case 'loading':
        return {
          icon: <RefreshCw className="w-3 h-3 animate-spin" />,
          color: 'text-blue-500',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30',
          borderColor: 'border-blue-200 dark:border-blue-700',
          label: 'Loading',
          description: message || 'Initializing AI services...'
        };
      
      case 'retrying':
        return {
          icon: <Clock className="w-3 h-3" />,
          color: 'text-orange-500',
          bgColor: 'bg-orange-100 dark:bg-orange-900/30',
          borderColor: 'border-orange-200 dark:border-orange-700',
          label: `Retrying (${retryCount}/3)`,
          description: message || 'Attempting to reconnect...'
        };
      
      case 'error':
        return {
          icon: <XCircle className="w-3 h-3" />,
          color: 'text-red-500',
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          borderColor: 'border-red-200 dark:border-red-700',
          label: 'Error',
          description: message || 'Service temporarily unavailable'
        };
      
      case 'offline':
        return {
          icon: <WifiOff className="w-3 h-3" />,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100 dark:bg-gray-900/30',
          borderColor: 'border-gray-200 dark:border-gray-700',
          label: 'Offline',
          description: message || 'No internet connection'
        };
      
      default: // 'online'
        return {
          icon: <CheckCircle className="w-3 h-3" />,
          color: 'text-green-500',
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          borderColor: 'border-green-200 dark:border-green-700',
          label: 'Online',
          description: message || 'All systems operational'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center gap-2 px-2 py-1 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className={`flex items-center justify-center ${config.color}`}>
        {config.icon}
      </div>
      <div className="flex flex-col min-w-0">
        <span className={`text-xs font-medium ${config.color}`}>
          {config.label}
        </span>
        {config.description && (
          <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
            {config.description}
          </span>
        )}
      </div>
    </div>
  );
};

/**
 * Compact version for header display
 */
export const CompactStatusIndicator: React.FC<{
  status: ChatbotStatus;
  retryCount?: number;
}> = ({ status, retryCount = 0 }) => {
  const getCompactConfig = () => {
    switch (status) {
      case 'ai-mode':
        return {
          dot: 'bg-green-400',
          label: 'AI Mode',
          pulse: false
        };
      
      case 'fallback-mode':
        return {
          dot: 'bg-yellow-400',
          label: 'Fallback Mode',
          pulse: false
        };
      
      case 'loading':
      case 'retrying':
        return {
          dot: 'bg-blue-400',
          label: status === 'retrying' ? `Retrying (${retryCount})` : 'Loading',
          pulse: true
        };
      
      case 'error':
        return {
          dot: 'bg-red-400',
          label: 'Error',
          pulse: false
        };
      
      case 'offline':
        return {
          dot: 'bg-gray-400',
          label: 'Offline',
          pulse: false
        };
      
      default:
        return {
          dot: 'bg-green-400',
          label: 'Online',
          pulse: false
        };
    }
  };

  const config = getCompactConfig();

  return (
    <div className="flex items-center gap-1.5">
      <div className={`w-2 h-2 rounded-full ${config.dot} ${config.pulse ? 'animate-pulse' : ''}`}></div>
      <span className="text-xs text-gray-300">
        {config.label}
      </span>
    </div>
  );
};

/**
 * Status banner for important notifications
 */
export const StatusBanner: React.FC<{
  status: ChatbotStatus;
  message: string;
  onDismiss?: () => void;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  }>;
}> = ({ status, message, onDismiss, actions }) => {
  const getBannerConfig = () => {
    switch (status) {
      case 'error':
        return {
          icon: <AlertTriangle className="w-4 h-4" />,
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          iconColor: 'text-red-600 dark:text-red-400'
        };
      
      case 'fallback-mode':
        return {
          icon: <Zap className="w-4 h-4" />,
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          textColor: 'text-yellow-800 dark:text-yellow-200',
          iconColor: 'text-yellow-600 dark:text-yellow-400'
        };
      
      default:
        return {
          icon: <CheckCircle className="w-4 h-4" />,
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          textColor: 'text-blue-800 dark:text-blue-200',
          iconColor: 'text-blue-600 dark:text-blue-400'
        };
    }
  };

  const config = getBannerConfig();

  return (
    <div className={`p-3 rounded-lg border ${config.bgColor} ${config.borderColor} mb-3`}>
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 ${config.iconColor}`}>
          {config.icon}
        </div>
        <div className="flex-1 min-w-0">
          <p className={`text-sm ${config.textColor}`}>
            {message}
          </p>
          {actions && actions.length > 0 && (
            <div className="flex gap-2 mt-2">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`text-xs px-2 py-1 rounded transition-colors ${
                    action.variant === 'primary'
                      ? `${config.bgColor} ${config.textColor} border ${config.borderColor} hover:opacity-80`
                      : `text-gray-600 dark:text-gray-400 hover:${config.textColor}`
                  }`}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className={`flex-shrink-0 ${config.iconColor} hover:opacity-70 transition-opacity`}
          >
            <XCircle className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default ChatbotStatusIndicator;
